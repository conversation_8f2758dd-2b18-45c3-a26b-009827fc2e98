import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'

// Authentication utilities
export const AUTH_ROLES = {
  ADMIN: 'admin',
  PROPRIETOR: 'proprietor',
  TEACHER: 'teacher',
  STUDENT: 'student'
}

// Check if user is authenticated
export const isAuthenticated = () => {
  if (typeof window === 'undefined') return false
  
  const token = localStorage.getItem('token')
  if (!token) return false
  
  try {
    const decoded = JSON.parse(Buffer.from(token, 'base64').toString())
    return decoded.exp > Date.now()
  } catch (error) {
    return false
  }
}

// Get current user from token
export const getCurrentUser = () => {
  if (typeof window === 'undefined') return null
  
  const userStr = localStorage.getItem('user')
  if (!userStr) return null
  
  try {
    return JSON.parse(userStr)
  } catch (error) {
    return null
  }
}

// Get token from localStorage
export const getToken = () => {
  if (typeof window === 'undefined') return null
  return localStorage.getItem('token')
}

// Logout user
export const logout = () => {
  if (typeof window === 'undefined') return
  
  localStorage.removeItem('token')
  localStorage.removeItem('user')
  window.location.href = '/login'
}

// Check if user has required role
export const hasRole = (requiredRole) => {
  const user = getCurrentUser()
  if (!user) return false
  
  // Admin has access to everything
  if (user.role === AUTH_ROLES.ADMIN) return true
  
  // Check specific role
  return user.role === requiredRole
}

// Role hierarchy for permissions
export const ROLE_HIERARCHY = {
  [AUTH_ROLES.ADMIN]: 4,
  [AUTH_ROLES.PROPRIETOR]: 3,
  [AUTH_ROLES.TEACHER]: 2,
  [AUTH_ROLES.STUDENT]: 1
}

// Check if user has minimum role level
export const hasMinimumRole = (minimumRole) => {
  const user = getCurrentUser()
  if (!user) return false
  
  const userLevel = ROLE_HIERARCHY[user.role] || 0
  const requiredLevel = ROLE_HIERARCHY[minimumRole] || 0
  
  return userLevel >= requiredLevel
}

// API request with authentication
export const authenticatedRequest = async (url, options = {}) => {
  const token = getToken()
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers
    }
  }
  
  const response = await fetch(url, { ...options, ...defaultOptions })
  
  // Handle unauthorized responses
  if (response.status === 401) {
    logout()
    throw new Error('Unauthorized')
  }
  
  return response
}

// Higher-order component for protecting routes
export const withAuth = (WrappedComponent, requiredRole = null) => {
  return function AuthenticatedComponent(props) {
    const [isLoading, setIsLoading] = useState(true)
    const [isAuthorized, setIsAuthorized] = useState(false)
    const router = useRouter()
    
    useEffect(() => {
      const checkAuth = () => {
        if (!isAuthenticated()) {
          router.push('/login')
          return
        }
        
        if (requiredRole && !hasRole(requiredRole)) {
          router.push('/unauthorized')
          return
        }
        
        setIsAuthorized(true)
        setIsLoading(false)
      }
      
      checkAuth()
    }, [router])
    
    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      )
    }
    
    if (!isAuthorized) {
      return null
    }
    
    return <WrappedComponent {...props} />
  }
}

// Dashboard route mapping
export const getDashboardRoute = (role) => {
  const routes = {
    [AUTH_ROLES.ADMIN]: '/dashboard/admin',
    [AUTH_ROLES.PROPRIETOR]: '/dashboard/proprietor',
    [AUTH_ROLES.TEACHER]: '/dashboard/teacher',
    [AUTH_ROLES.STUDENT]: '/dashboard/student'
  }
  
  return routes[role] || '/dashboard'
}

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const [isClosing, setIsClosing] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 20
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled)
      }
    }

    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMenuOpen(false)
        setIsClosing(false)
      }
    }

    window.addEventListener('scroll', handleScroll)
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleResize)
    }
  }, [scrolled])

  const handleMenuToggle = () => {
    if (isMenuOpen) {
      setIsClosing(true)
      setTimeout(() => {
        setIsMenuOpen(false)
        setIsClosing(false)
      }, 300)
    } else {
      setIsMenuOpen(true)
    }
  }

  const isActive = (path) => {
    return router.pathname === path
  }

  return (
    <header className={`fixed w-full transition-all duration-300 ${
      scrolled ? 'bg-white/95 backdrop-blur-sm shadow-lg' : 'bg-white'
    } sticky top-0 z-50`}>
      <div className="container">
        <div className="flex justify-between items-center py-4">
          <Link href="/" className="flex items-center space-x-3 group">
            <img 
              src="/GHC.jpg" 
              alt="Great Heritage College" 
              className="h-12 w-12 rounded-full transition-transform duration-300 group-hover:scale-105" 
            />
            <span className="text-lg lg:text-xl xl:text-2xl font-bold text-primary transition-colors duration-300 group-hover:text-primary/80 whitespace-nowrap">
              Great Heritage College
            </span>
          </Link>
          
          <nav className="hidden lg:flex items-center">
            {[
              { href: '/', label: 'Home' },
              { href: '/about', label: 'About' },
              { href: '/academics', label: 'Academics' },
              { href: '/student-life', label: 'Student Life' },
              { href: '/admissions', label: 'Admissions' },
              { href: '/contact', label: 'Contact' }
            ].map(({ href, label }) => (
              <Link 
                key={href} 
                href={href} 
                className={`relative py-2 px-3 xl:px-4 text-sm xl:text-base text-dark transition-colors duration-300
                  whitespace-nowrap after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-0 
                  after:bg-primary after:transition-all after:duration-300 hover:text-primary 
                  hover:after:w-full ${isActive(href) ? 'text-primary after:w-full' : ''}`}
              >
                {label}
              </Link>
            ))}
          </nav>
          
          <div className="hidden lg:flex items-center space-x-3">
            <Link
              href="/login"
              className="flex items-center space-x-2 px-4 py-2 text-primary border border-primary rounded-lg hover:bg-primary hover:text-white transition-all duration-300 font-medium"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span>Portal</span>
            </Link>
            <Link
              href="/apply"
              className="btn btn-primary transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5"
            >
              Apply Now
            </Link>
          </div>
          
          <button 
            className="lg:hidden w-10 h-10 relative focus:outline-none"
            onClick={handleMenuToggle}
            aria-label="Toggle menu"
          >
            <div className="absolute inset-0 flex flex-col justify-center items-center">
              <span className={`w-6 h-0.5 bg-dark transition-all duration-300 transform
                ${isMenuOpen ? 'rotate-45 translate-y-1.5' : ''} 
                ${isClosing ? '-rotate-45 translate-y-0' : ''}`}
              />
              <span className={`w-6 h-0.5 bg-dark transition-all duration-300 transform my-1.5
                ${isMenuOpen || isClosing ? 'opacity-0 scale-0' : 'opacity-100 scale-100'}`}
              />
              <span className={`w-6 h-0.5 bg-dark transition-all duration-300 transform
                ${isMenuOpen ? '-rotate-45 -translate-y-1.5' : ''} 
                ${isClosing ? 'rotate-45 translate-y-0' : ''}`}
              />
            </div>
          </button>
        </div>
        
        <div className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out
          ${isMenuOpen ? 'max-h-[400px] opacity-100' : 'max-h-0 opacity-0'}
          ${isClosing ? 'animate-slideUp' : isMenuOpen ? 'animate-slideDown' : ''}`}>
          <nav className="flex flex-col py-4">
            {[
              { href: '/', label: 'Home' },
              { href: '/about', label: 'About' },
              { href: '/academics', label: 'Academics' },
              { href: '/student-life', label: 'Student Life' },
              { href: '/admissions', label: 'Admissions' },
              { href: '/contact', label: 'Contact' }
            ].map(({ href, label }, index) => (
              <Link 
                key={href} 
                href={href} 
                className={`py-3 px-4 text-dark transition-all duration-300 
                  hover:bg-primary/5 hover:text-primary border-l-4 
                  ${isActive(href) 
                    ? 'text-primary border-primary bg-primary/5' 
                    : 'border-transparent'
                  }
                  ${isMenuOpen ? 'animate-fadeInRight' : ''}
                  animation-delay-${index * 100}`}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                {label}
              </Link>
            ))}
            <div className="pt-4 px-4 space-y-3">
              <Link
                href="/login"
                className="flex items-center justify-center space-x-2 w-full py-3 px-4 text-primary border-2 border-primary
                  hover:bg-primary hover:text-white transition-all duration-300 rounded-lg font-medium
                  ${isMenuOpen ? 'animate-fadeInUp' : ''}"
                style={{ animationDelay: '350ms' }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span>Student/Staff Portal</span>
              </Link>
              <Link
                href="/apply"
                className="btn btn-primary w-full text-center transition-all duration-300
                  hover:shadow-lg hover:-translate-y-0.5
                  ${isMenuOpen ? 'animate-fadeInUp' : ''}"
                style={{ animationDelay: '400ms' }}
              >
                Apply Now
              </Link>
            </div>
          </nav>
        </div>
      </div>
    </header>
  )
}
import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import Image from 'next/image'
import { getCurrentUser, logout, AUTH_ROLES } from '../lib/auth'

export default function DashboardLayout({ children }) {
  const [user, setUser] = useState(null)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const currentUser = getCurrentUser()
    setUser(currentUser)
  }, [])

  const handleLogout = () => {
    logout()
  }

  // Navigation items based on user role
  const getNavigationItems = (role) => {
    const baseItems = [
      { name: 'Dashboard', href: `/dashboard/${role}`, icon: '📊' }
    ]

    switch (role) {
      case AUTH_ROLES.ADMIN:
        return [
          ...baseItems,
          { name: 'User Management', href: '/dashboard/admin/users', icon: '👥' },
          { name: 'School Settings', href: '/dashboard/admin/settings', icon: '⚙️' },
          { name: 'Academic Setup', href: '/dashboard/admin/academic', icon: '🎓' },
          { name: 'Finance Reports', href: '/dashboard/admin/finance', icon: '💰' },
          { name: 'System Logs', href: '/dashboard/admin/logs', icon: '📋' }
        ]
      
      case AUTH_ROLES.PROPRIETOR:
        return [
          ...baseItems,
          { name: 'Academic Overview', href: '/dashboard/proprietor/academic', icon: '📚' },
          { name: 'Student Performance', href: '/dashboard/proprietor/performance', icon: '📈' },
          { name: 'Staff Management', href: '/dashboard/proprietor/staff', icon: '👨‍🏫' },
          { name: 'Finance Summary', href: '/dashboard/proprietor/finance', icon: '💰' },
          { name: 'Events & Calendar', href: '/dashboard/proprietor/events', icon: '📅' },
          { name: 'Communications', href: '/dashboard/proprietor/communications', icon: '📢' }
        ]
      
      case AUTH_ROLES.TEACHER:
        return [
          ...baseItems,
          { name: 'My Classes', href: '/dashboard/teacher/classes', icon: '🏫' },
          { name: 'Attendance', href: '/dashboard/teacher/attendance', icon: '✅' },
          { name: 'Gradebook', href: '/dashboard/teacher/grades', icon: '📝' },
          { name: 'E-Classroom', href: '/dashboard/teacher/classroom', icon: '💻' },
          { name: 'Assignments', href: '/dashboard/teacher/assignments', icon: '📄' },
          { name: 'Messages', href: '/dashboard/teacher/messages', icon: '💬' }
        ]
      
      case AUTH_ROLES.STUDENT:
        return [
          ...baseItems,
          { name: 'My Grades', href: '/dashboard/student/grades', icon: '📊' },
          { name: 'Attendance', href: '/dashboard/student/attendance', icon: '📅' },
          { name: 'Assignments', href: '/dashboard/student/assignments', icon: '📝' },
          { name: 'E-Learning', href: '/dashboard/student/learning', icon: '💻' },
          { name: 'Messages', href: '/dashboard/student/messages', icon: '💬' },
          { name: 'Fee Payment', href: '/dashboard/student/payments', icon: '💳' }
        ]
      
      default:
        return baseItems
    }
  }

  const navigationItems = user ? getNavigationItems(user.role) : []

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } lg:static lg:inset-0`}>
        <div className="flex items-center justify-center h-16 px-4 bg-primary">
          <Link href="/" className="flex items-center space-x-2">
            <Image 
              src="/GHC.jpg" 
              alt="Great Heritage College" 
              width={32}
              height={32}
              className="rounded-full" 
            />
            <span className="text-white font-bold text-lg">GHC Portal</span>
          </Link>
        </div>
        
        <nav className="mt-8">
          {navigationItems.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={`flex items-center px-6 py-3 text-gray-700 hover:bg-primary hover:text-white transition-colors duration-200 ${
                router.pathname === item.href ? 'bg-primary text-white' : ''
              }`}
            >
              <span className="mr-3 text-lg">{item.icon}</span>
              {item.name}
            </Link>
          ))}
        </nav>
      </div>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Top navigation */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-4">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
              <h1 className="ml-4 text-xl font-semibold text-gray-900 capitalize">
                {user.role} Dashboard
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{user.name}</p>
                <p className="text-xs text-gray-500 capitalize">{user.role}</p>
              </div>
              <button
                onClick={handleLogout}
                className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                title="Logout"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </button>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  )
}

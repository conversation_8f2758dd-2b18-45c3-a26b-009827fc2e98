"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard/admin",{

/***/ "./components/DashboardLayout.js":
/*!***************************************!*\
  !*** ./components/DashboardLayout.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/auth */ \"./lib/auth.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction DashboardLayout(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_5__.getCurrentUser)();\n        setUser(currentUser);\n    }, []);\n    const handleLogout = ()=>{\n        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_5__.logout)();\n    };\n    // Navigation items based on user role\n    // Professional SVG Icons for Navigation\n    const DashboardIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 25,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 26,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 24,\n            columnNumber: 5\n        }, this);\n    const UsersIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 31,\n            columnNumber: 5\n        }, this);\n    const CogIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 38,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 39,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 37,\n            columnNumber: 5\n        }, this);\n    const AcademicIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 14l9-5-9-5-9 5 9 5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 45,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 46,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 44,\n            columnNumber: 5\n        }, this);\n    const ChartIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 51,\n            columnNumber: 5\n        }, this);\n    const getNavigationItems = (role)=>{\n        const baseItems = [\n            {\n                name: \"Dashboard\",\n                href: \"/dashboard/\".concat(role),\n                icon: DashboardIcon\n            }\n        ];\n        switch(role){\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.ADMIN:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"User Management\",\n                        href: \"/dashboard/admin/users\",\n                        icon: UsersIcon\n                    },\n                    {\n                        name: \"School Settings\",\n                        href: \"/dashboard/admin/settings\",\n                        icon: CogIcon\n                    },\n                    {\n                        name: \"Academic Setup\",\n                        href: \"/dashboard/admin/academic\",\n                        icon: AcademicIcon\n                    },\n                    {\n                        name: \"Finance Reports\",\n                        href: \"/dashboard/admin/finance\",\n                        icon: ChartIcon\n                    }\n                ];\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.PROPRIETOR:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"Academic Overview\",\n                        href: \"/dashboard/proprietor/academic\",\n                        icon: AcademicIcon\n                    },\n                    {\n                        name: \"Student Performance\",\n                        href: \"/dashboard/proprietor/performance\",\n                        icon: ChartIcon\n                    },\n                    {\n                        name: \"Staff Management\",\n                        href: \"/dashboard/proprietor/staff\",\n                        icon: UsersIcon\n                    },\n                    {\n                        name: \"Finance Summary\",\n                        href: \"/dashboard/proprietor/finance\",\n                        icon: ChartIcon\n                    }\n                ];\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.TEACHER:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"My Classes\",\n                        href: \"/dashboard/teacher/classes\",\n                        icon: AcademicIcon\n                    },\n                    {\n                        name: \"Attendance\",\n                        href: \"/dashboard/teacher/attendance\",\n                        icon: ChartIcon\n                    },\n                    {\n                        name: \"Gradebook\",\n                        href: \"/dashboard/teacher/grades\",\n                        icon: ChartIcon\n                    },\n                    {\n                        name: \"E-Classroom\",\n                        href: \"/dashboard/teacher/classroom\",\n                        icon: AcademicIcon\n                    }\n                ];\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.STUDENT:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"My Grades\",\n                        href: \"/dashboard/student/grades\",\n                        icon: ChartIcon\n                    },\n                    {\n                        name: \"Attendance\",\n                        href: \"/dashboard/student/attendance\",\n                        icon: ChartIcon\n                    },\n                    {\n                        name: \"Assignments\",\n                        href: \"/dashboard/student/assignments\",\n                        icon: AcademicIcon\n                    },\n                    {\n                        name: \"E-Learning\",\n                        href: \"/dashboard/student/learning\",\n                        icon: AcademicIcon\n                    }\n                ];\n            default:\n                return baseItems;\n        }\n    };\n    const navigationItems = user ? getNavigationItems(user.role) : [];\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 \".concat(sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\", \" lg:static lg:inset-0\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-16 px-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: \"/GHC.jpg\",\n                                    alt: \"Great Heritage College\",\n                                    width: 36,\n                                    height: 36,\n                                    className: \"rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary font-bold text-lg block\",\n                                            children: \"GHC Portal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 capitalize\",\n                                            children: [\n                                                user === null || user === void 0 ? void 0 : user.role,\n                                                \" Panel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-8 px-4\",\n                        children: navigationItems.map((item)=>{\n                            const IconComponent = item.icon;\n                            const isActive = router.pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: item.href,\n                                className: \"flex items-center px-4 py-3 mb-2 rounded-lg transition-all duration-200 \".concat(isActive ? \"bg-primary text-white shadow-md\" : \"text-gray-700 hover:bg-gray-100 hover:text-primary\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-3 font-medium\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, item.name, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:ml-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white border-b border-gray-200 shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                            className: \"lg:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        \"Good \",\n                                                        new Date().getHours() < 12 ? \"Morning\" : new Date().getHours() < 18 ? \"Afternoon\" : \"Evening\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        user.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12M1.01 11.88l3.12 3.12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 px-3 py-2 rounded-lg bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-primary rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-semibold text-sm\",\n                                                        children: user.name.charAt(0).toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 capitalize\",\n                                                            children: user.role\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"p-2 rounded-lg text-gray-600 hover:text-red-600 hover:bg-red-50 transition-colors\",\n                                            title: \"Logout\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-6 bg-gray-50 min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"ozsJIlFkk/uDHDHvUAZAgY3zAf4=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/DashboardLayout.js\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard/admin",{

/***/ "./pages/dashboard/admin.js":
/*!**********************************!*\
  !*** ./pages/dashboard/admin.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/DashboardLayout */ \"./components/DashboardLayout.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/auth */ \"./lib/auth.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n// Professional SVG Icons\nconst UsersIcon = (param)=>/*#__PURE__*/ {\n    let { className } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 9,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined);\n};\n_c = UsersIcon;\nconst AcademicCapIcon = (param)=>/*#__PURE__*/ {\n    let { className } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l9-5-9-5-9 5 9 5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                lineNumber: 15,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                lineNumber: 16,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = AcademicCapIcon;\nconst BuildingIcon = (param)=>/*#__PURE__*/ {\n    let { className } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 22,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = BuildingIcon;\nconst CurrencyIcon = (param)=>/*#__PURE__*/ {\n    let { className } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 28,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = CurrencyIcon;\nconst ChartBarIcon = (param)=>/*#__PURE__*/ {\n    let { className } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = ChartBarIcon;\nconst CogIcon = (param)=>/*#__PURE__*/ {\n    let { className } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                lineNumber: 40,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                lineNumber: 41,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\n};\n_c5 = CogIcon;\nfunction AdminDashboard() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalStudents: 0,\n        totalTeachers: 0,\n        totalClasses: 0,\n        pendingPayments: 0\n    });\n    const [recentActivities, setRecentActivities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check authentication and authorization\n        if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.isAuthenticated)() || !(0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.hasRole)(_lib_auth__WEBPACK_IMPORTED_MODULE_4__.AUTH_ROLES.ADMIN)) {\n            router.push(\"/login\");\n            return;\n        }\n        // Load dashboard data\n        loadDashboardData();\n    }, [\n        router\n    ]);\n    const loadDashboardData = async ()=>{\n        try {\n            // Mock data - replace with actual API calls\n            setStats({\n                totalStudents: 847,\n                totalTeachers: 45,\n                totalClasses: 24,\n                pendingPayments: 23\n            });\n            setRecentActivities([\n                {\n                    id: 1,\n                    type: \"user\",\n                    message: \"New teacher John Doe registered\",\n                    time: \"2 hours ago\"\n                },\n                {\n                    id: 2,\n                    type: \"payment\",\n                    message: \"15 fee payments received\",\n                    time: \"4 hours ago\"\n                },\n                {\n                    id: 3,\n                    type: \"system\",\n                    message: \"Database backup completed\",\n                    time: \"6 hours ago\"\n                },\n                {\n                    id: 4,\n                    type: \"academic\",\n                    message: \"SS3 results published\",\n                    time: \"1 day ago\"\n                },\n                {\n                    id: 5,\n                    type: \"communication\",\n                    message: \"Bulk SMS sent to parents\",\n                    time: \"2 days ago\"\n                }\n            ]);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const quickActions = [\n        {\n            name: \"User Management\",\n            href: \"/dashboard/admin/users\",\n            icon: UsersIcon,\n            color: \"bg-blue-500\",\n            description: \"Manage users and roles\"\n        },\n        {\n            name: \"Academic Setup\",\n            href: \"/dashboard/admin/academic\",\n            icon: AcademicCapIcon,\n            color: \"bg-green-500\",\n            description: \"Classes, subjects & sessions\"\n        },\n        {\n            name: \"Reports & Analytics\",\n            href: \"/dashboard/admin/reports\",\n            icon: ChartBarIcon,\n            color: \"bg-purple-500\",\n            description: \"View system reports\"\n        },\n        {\n            name: \"System Settings\",\n            href: \"/dashboard/admin/settings\",\n            icon: CogIcon,\n            color: \"bg-gray-600\",\n            description: \"Configure system\"\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold mb-2\",\n                                        children: \"Administrator Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-100\",\n                                        children: \"Manage your school system efficiently\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CogIcon, {\n                                        className: \"w-12 h-12 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                children: \"Total Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: stats.totalStudents\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-600 mt-1\",\n                                                children: \"↗ 12% from last month\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-blue-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UsersIcon, {\n                                            className: \"w-8 h-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                children: \"Total Teachers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: stats.totalTeachers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-600 mt-1\",\n                                                children: \"↗ 3% from last month\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                            className: \"w-8 h-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                children: \"Active Classes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: stats.totalClasses\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"Across all levels\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-purple-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BuildingIcon, {\n                                            className: \"w-8 h-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                children: \"Pending Payments\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: stats.pendingPayments\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-orange-600 mt-1\",\n                                                children: \"Requires attention\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-orange-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrencyIcon, {\n                                            className: \"w-8 h-8 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Quick Actions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Manage your school system\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: quickActions.map((action)=>{\n                                const IconComponent = action.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: action.href,\n                                    className: \"group relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 hover:from-white hover:to-gray-50 border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg \".concat(action.color, \" group-hover:scale-110 transition-transform duration-300\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900 group-hover:text-primary transition-colors\",\n                                                            children: action.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: action.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 220,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 text-gray-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 5l7 7-7 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 232,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 231,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, action.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 215,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Recent Activities\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: recentActivities.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center p-3 rounded-lg bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-full \".concat(activity.type === \"user\" ? \"bg-blue-100 text-blue-600\" : activity.type === \"payment\" ? \"bg-green-100 text-green-600\" : activity.type === \"system\" ? \"bg-gray-100 text-gray-600\" : activity.type === \"academic\" ? \"bg-purple-100 text-purple-600\" : \"bg-orange-100 text-orange-600\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: activity.type === \"user\" ? \"\\uD83D\\uDC64\" : activity.type === \"payment\" ? \"\\uD83D\\uDCB0\" : activity.type === \"system\" ? \"⚙️\" : activity.type === \"academic\" ? \"\\uD83D\\uDCDA\" : \"\\uD83D\\uDCE2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: activity.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: activity.time\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, activity.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"nt0VM5IhOPP9B42EPm7aXYDGk1M=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c6 = AdminDashboard;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"UsersIcon\");\n$RefreshReg$(_c1, \"AcademicCapIcon\");\n$RefreshReg$(_c2, \"BuildingIcon\");\n$RefreshReg$(_c3, \"CurrencyIcon\");\n$RefreshReg$(_c4, \"ChartBarIcon\");\n$RefreshReg$(_c5, \"CogIcon\");\n$RefreshReg$(_c6, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard/admin.js\n"));

/***/ })

});
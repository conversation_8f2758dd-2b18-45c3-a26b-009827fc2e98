/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/dashboard/admin";
exports.ids = ["pages/dashboard/admin"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\dashboard\\admin.js */ \"./pages/dashboard/admin.js\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/dashboard/admin\",\n        pathname: \"/dashboard/admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_dashboard_admin_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/DashboardLayout.js":
/*!***************************************!*\
  !*** ./components/DashboardLayout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/auth */ \"./lib/auth.js\");\n\n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_5__.getCurrentUser)();\n        setUser(currentUser);\n    }, []);\n    const handleLogout = ()=>{\n        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_5__.logout)();\n    };\n    // Navigation items based on user role\n    const getNavigationItems = (role)=>{\n        const baseItems = [\n            {\n                name: \"Dashboard\",\n                href: `/dashboard/${role}`,\n                icon: \"\\uD83D\\uDCCA\"\n            }\n        ];\n        switch(role){\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.ADMIN:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"User Management\",\n                        href: \"/dashboard/admin/users\",\n                        icon: \"\\uD83D\\uDC65\"\n                    },\n                    {\n                        name: \"School Settings\",\n                        href: \"/dashboard/admin/settings\",\n                        icon: \"⚙️\"\n                    },\n                    {\n                        name: \"Academic Setup\",\n                        href: \"/dashboard/admin/academic\",\n                        icon: \"\\uD83C\\uDF93\"\n                    },\n                    {\n                        name: \"Finance Reports\",\n                        href: \"/dashboard/admin/finance\",\n                        icon: \"\\uD83D\\uDCB0\"\n                    },\n                    {\n                        name: \"System Logs\",\n                        href: \"/dashboard/admin/logs\",\n                        icon: \"\\uD83D\\uDCCB\"\n                    }\n                ];\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.PROPRIETOR:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"Academic Overview\",\n                        href: \"/dashboard/proprietor/academic\",\n                        icon: \"\\uD83D\\uDCDA\"\n                    },\n                    {\n                        name: \"Student Performance\",\n                        href: \"/dashboard/proprietor/performance\",\n                        icon: \"\\uD83D\\uDCC8\"\n                    },\n                    {\n                        name: \"Staff Management\",\n                        href: \"/dashboard/proprietor/staff\",\n                        icon: \"\\uD83D\\uDC68‍\\uD83C\\uDFEB\"\n                    },\n                    {\n                        name: \"Finance Summary\",\n                        href: \"/dashboard/proprietor/finance\",\n                        icon: \"\\uD83D\\uDCB0\"\n                    },\n                    {\n                        name: \"Events & Calendar\",\n                        href: \"/dashboard/proprietor/events\",\n                        icon: \"\\uD83D\\uDCC5\"\n                    },\n                    {\n                        name: \"Communications\",\n                        href: \"/dashboard/proprietor/communications\",\n                        icon: \"\\uD83D\\uDCE2\"\n                    }\n                ];\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.TEACHER:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"My Classes\",\n                        href: \"/dashboard/teacher/classes\",\n                        icon: \"\\uD83C\\uDFEB\"\n                    },\n                    {\n                        name: \"Attendance\",\n                        href: \"/dashboard/teacher/attendance\",\n                        icon: \"✅\"\n                    },\n                    {\n                        name: \"Gradebook\",\n                        href: \"/dashboard/teacher/grades\",\n                        icon: \"\\uD83D\\uDCDD\"\n                    },\n                    {\n                        name: \"E-Classroom\",\n                        href: \"/dashboard/teacher/classroom\",\n                        icon: \"\\uD83D\\uDCBB\"\n                    },\n                    {\n                        name: \"Assignments\",\n                        href: \"/dashboard/teacher/assignments\",\n                        icon: \"\\uD83D\\uDCC4\"\n                    },\n                    {\n                        name: \"Messages\",\n                        href: \"/dashboard/teacher/messages\",\n                        icon: \"\\uD83D\\uDCAC\"\n                    }\n                ];\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.STUDENT:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"My Grades\",\n                        href: \"/dashboard/student/grades\",\n                        icon: \"\\uD83D\\uDCCA\"\n                    },\n                    {\n                        name: \"Attendance\",\n                        href: \"/dashboard/student/attendance\",\n                        icon: \"\\uD83D\\uDCC5\"\n                    },\n                    {\n                        name: \"Assignments\",\n                        href: \"/dashboard/student/assignments\",\n                        icon: \"\\uD83D\\uDCDD\"\n                    },\n                    {\n                        name: \"E-Learning\",\n                        href: \"/dashboard/student/learning\",\n                        icon: \"\\uD83D\\uDCBB\"\n                    },\n                    {\n                        name: \"Messages\",\n                        href: \"/dashboard/student/messages\",\n                        icon: \"\\uD83D\\uDCAC\"\n                    },\n                    {\n                        name: \"Fee Payment\",\n                        href: \"/dashboard/student/payments\",\n                        icon: \"\\uD83D\\uDCB3\"\n                    }\n                ];\n            default:\n                return baseItems;\n        }\n    };\n    const navigationItems = user ? getNavigationItems(user.role) : [];\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"} lg:static lg:inset-0`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-16 px-4 bg-primary\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: \"/GHC.jpg\",\n                                    alt: \"Great Heritage College\",\n                                    width: 32,\n                                    height: 32,\n                                    className: \"rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-lg\",\n                                    children: \"GHC Portal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-8\",\n                        children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: item.href,\n                                className: `flex items-center px-6 py-3 text-gray-700 hover:bg-primary hover:text-white transition-colors duration-200 ${router.pathname === item.href ? \"bg-primary text-white\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-3 text-lg\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    item.name\n                                ]\n                            }, item.name, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:ml-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white shadow-sm border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-4 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                            className: \"lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"ml-4 text-xl font-semibold text-gray-900 capitalize\",\n                                            children: [\n                                                user.role,\n                                                \" Dashboard\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 capitalize\",\n                                                    children: user.role\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                            title: \"Logout\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/DashboardLayout.js\n");

/***/ }),

/***/ "./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_ROLES: () => (/* binding */ AUTH_ROLES),\n/* harmony export */   ROLE_HIERARCHY: () => (/* binding */ ROLE_HIERARCHY),\n/* harmony export */   authenticatedRequest: () => (/* binding */ authenticatedRequest),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getDashboardRoute: () => (/* binding */ getDashboardRoute),\n/* harmony export */   getToken: () => (/* binding */ getToken),\n/* harmony export */   hasMinimumRole: () => (/* binding */ hasMinimumRole),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Authentication utilities\nconst AUTH_ROLES = {\n    ADMIN: \"admin\",\n    PROPRIETOR: \"proprietor\",\n    TEACHER: \"teacher\",\n    STUDENT: \"student\"\n};\n// Check if user is authenticated\nconst isAuthenticated = ()=>{\n    if (true) return false;\n    const token = localStorage.getItem(\"token\");\n    if (!token) return false;\n    try {\n        const decoded = JSON.parse(Buffer.from(token, \"base64\").toString());\n        return decoded.exp > Date.now();\n    } catch (error) {\n        return false;\n    }\n};\n// Get current user from token\nconst getCurrentUser = ()=>{\n    if (true) return null;\n    const userStr = localStorage.getItem(\"user\");\n    if (!userStr) return null;\n    try {\n        return JSON.parse(userStr);\n    } catch (error) {\n        return null;\n    }\n};\n// Get token from localStorage\nconst getToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"token\");\n};\n// Logout user\nconst logout = ()=>{\n    if (true) return;\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    window.location.href = \"/login\";\n};\n// Check if user has required role\nconst hasRole = (requiredRole)=>{\n    const user = getCurrentUser();\n    if (!user) return false;\n    // Admin has access to everything\n    if (user.role === AUTH_ROLES.ADMIN) return true;\n    // Check specific role\n    return user.role === requiredRole;\n};\n// Role hierarchy for permissions\nconst ROLE_HIERARCHY = {\n    [AUTH_ROLES.ADMIN]: 4,\n    [AUTH_ROLES.PROPRIETOR]: 3,\n    [AUTH_ROLES.TEACHER]: 2,\n    [AUTH_ROLES.STUDENT]: 1\n};\n// Check if user has minimum role level\nconst hasMinimumRole = (minimumRole)=>{\n    const user = getCurrentUser();\n    if (!user) return false;\n    const userLevel = ROLE_HIERARCHY[user.role] || 0;\n    const requiredLevel = ROLE_HIERARCHY[minimumRole] || 0;\n    return userLevel >= requiredLevel;\n};\n// API request with authentication\nconst authenticatedRequest = async (url, options = {})=>{\n    const token = getToken();\n    const defaultOptions = {\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...token && {\n                Authorization: `Bearer ${token}`\n            },\n            ...options.headers\n        }\n    };\n    const response = await fetch(url, {\n        ...options,\n        ...defaultOptions\n    });\n    // Handle unauthorized responses\n    if (response.status === 401) {\n        logout();\n        throw new Error(\"Unauthorized\");\n    }\n    return response;\n};\n// Higher-order component for protecting routes\nconst withAuth = (WrappedComponent, requiredRole = null)=>{\n    return function AuthenticatedComponent(props) {\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const [isAuthorized, setIsAuthorized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            const checkAuth = ()=>{\n                if (!isAuthenticated()) {\n                    router.push(\"/login\");\n                    return;\n                }\n                if (requiredRole && !hasRole(requiredRole)) {\n                    router.push(\"/unauthorized\");\n                    return;\n                }\n                setIsAuthorized(true);\n                setIsLoading(false);\n            };\n            checkAuth();\n        }, [\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\lib\\\\auth.js\",\n                    lineNumber: 139,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\lib\\\\auth.js\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthorized) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\lib\\\\auth.js\",\n            lineNumber: 148,\n            columnNumber: 12\n        }, this);\n    };\n};\n// Dashboard route mapping\nconst getDashboardRoute = (role)=>{\n    const routes = {\n        [AUTH_ROLES.ADMIN]: \"/dashboard/admin\",\n        [AUTH_ROLES.PROPRIETOR]: \"/dashboard/proprietor\",\n        [AUTH_ROLES.TEACHER]: \"/dashboard/teacher\",\n        [AUTH_ROLES.STUDENT]: \"/dashboard/student\"\n    };\n    return routes[role] || \"/dashboard\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/auth.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE4QjtBQUVmLFNBQVNBLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDbEQscUJBQU8sOERBQUNEO1FBQVcsR0FBR0MsU0FBUzs7Ozs7O0FBQ2pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3JlYXQtaGVyaXRhZ2UtY29sbGVnZS1mcm9udGVuZC8uL3BhZ2VzL19hcHAuanM/ZTBhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxufSJdLCJuYW1lcyI6WyJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/dashboard/admin.js":
/*!**********************************!*\
  !*** ./pages/dashboard/admin.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/DashboardLayout */ \"./components/DashboardLayout.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/auth */ \"./lib/auth.js\");\n\n\n\n\n\nfunction AdminDashboard() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalStudents: 0,\n        totalTeachers: 0,\n        totalClasses: 0,\n        pendingPayments: 0\n    });\n    const [recentActivities, setRecentActivities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check authentication and authorization\n        if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.isAuthenticated)() || !(0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.hasRole)(_lib_auth__WEBPACK_IMPORTED_MODULE_4__.AUTH_ROLES.ADMIN)) {\n            router.push(\"/login\");\n            return;\n        }\n        // Load dashboard data\n        loadDashboardData();\n    }, [\n        router\n    ]);\n    const loadDashboardData = async ()=>{\n        try {\n            // Mock data - replace with actual API calls\n            setStats({\n                totalStudents: 847,\n                totalTeachers: 45,\n                totalClasses: 24,\n                pendingPayments: 23\n            });\n            setRecentActivities([\n                {\n                    id: 1,\n                    type: \"user\",\n                    message: \"New teacher John Doe registered\",\n                    time: \"2 hours ago\"\n                },\n                {\n                    id: 2,\n                    type: \"payment\",\n                    message: \"15 fee payments received\",\n                    time: \"4 hours ago\"\n                },\n                {\n                    id: 3,\n                    type: \"system\",\n                    message: \"Database backup completed\",\n                    time: \"6 hours ago\"\n                },\n                {\n                    id: 4,\n                    type: \"academic\",\n                    message: \"SS3 results published\",\n                    time: \"1 day ago\"\n                },\n                {\n                    id: 5,\n                    type: \"communication\",\n                    message: \"Bulk SMS sent to parents\",\n                    time: \"2 days ago\"\n                }\n            ]);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const quickActions = [\n        {\n            name: \"Add New User\",\n            href: \"/dashboard/admin/users/new\",\n            icon: \"\\uD83D\\uDC64\",\n            color: \"bg-blue-500\"\n        },\n        {\n            name: \"Manage Classes\",\n            href: \"/dashboard/admin/academic/classes\",\n            icon: \"\\uD83C\\uDFEB\",\n            color: \"bg-green-500\"\n        },\n        {\n            name: \"View Reports\",\n            href: \"/dashboard/admin/reports\",\n            icon: \"\\uD83D\\uDCCA\",\n            color: \"bg-purple-500\"\n        },\n        {\n            name: \"System Settings\",\n            href: \"/dashboard/admin/settings\",\n            icon: \"⚙️\",\n            color: \"bg-gray-500\"\n        },\n        {\n            name: \"Send Notifications\",\n            href: \"/dashboard/admin/notifications\",\n            icon: \"\\uD83D\\uDCE2\",\n            color: \"bg-orange-500\"\n        },\n        {\n            name: \"Backup Data\",\n            href: \"/dashboard/admin/backup\",\n            icon: \"\\uD83D\\uDCBE\",\n            color: \"bg-red-500\"\n        }\n    ];\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Welcome, Administrator\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Here's an overview of your school management system.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full bg-blue-100 text-blue-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDC68‍\\uD83C\\uDF93\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.totalStudents\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full bg-green-100 text-green-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDC68‍\\uD83C\\uDFEB\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Teachers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.totalTeachers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full bg-purple-100 text-purple-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83C\\uDFEB\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Classes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.totalClasses\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full bg-orange-100 text-orange-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDCB0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Pending Payments\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.pendingPayments\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Quick Actions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4\",\n                            children: quickActions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: action.href,\n                                    className: \"flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `p-3 rounded-full ${action.color} text-white mb-2`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: action.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-900 text-center\",\n                                            children: action.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, action.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Recent Activities\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: recentActivities.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center p-3 rounded-lg bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `p-2 rounded-full ${activity.type === \"user\" ? \"bg-blue-100 text-blue-600\" : activity.type === \"payment\" ? \"bg-green-100 text-green-600\" : activity.type === \"system\" ? \"bg-gray-100 text-gray-600\" : activity.type === \"academic\" ? \"bg-purple-100 text-purple-600\" : \"bg-orange-100 text-orange-600\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: activity.type === \"user\" ? \"\\uD83D\\uDC64\" : activity.type === \"payment\" ? \"\\uD83D\\uDCB0\" : activity.type === \"system\" ? \"⚙️\" : activity.type === \"academic\" ? \"\\uD83D\\uDCDA\" : \"\\uD83D\\uDCE2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: activity.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: activity.time\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, activity.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\admin.js\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard/admin.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fadmin&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cadmin.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
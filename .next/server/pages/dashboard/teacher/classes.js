/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/dashboard/teacher/classes";
exports.ids = ["pages/dashboard/teacher/classes"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fteacher%2Fclasses&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cteacher%5Cclasses.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fteacher%2Fclasses&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cteacher%5Cclasses.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\dashboard\\teacher\\classes.js */ \"./pages/dashboard/teacher/classes.js\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/dashboard/teacher/classes\",\n        pathname: \"/dashboard/teacher/classes\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_dashboard_teacher_classes_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fteacher%2Fclasses&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cteacher%5Cclasses.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/DashboardLayout.js":
/*!***************************************!*\
  !*** ./components/DashboardLayout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/auth */ \"./lib/auth.js\");\n\n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_5__.getCurrentUser)();\n        setUser(currentUser);\n    }, []);\n    const handleLogout = ()=>{\n        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_5__.logout)();\n    };\n    // Navigation items based on user role\n    // Professional SVG Icons for Navigation\n    const DashboardIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 25,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 26,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 24,\n            columnNumber: 5\n        }, this);\n    const UsersIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 31,\n            columnNumber: 5\n        }, this);\n    const CogIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 38,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 39,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 37,\n            columnNumber: 5\n        }, this);\n    const AcademicIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 14l9-5-9-5-9 5 9 5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 45,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                    lineNumber: 46,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 44,\n            columnNumber: 5\n        }, this);\n    const ChartIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 51,\n            columnNumber: 5\n        }, this);\n    const getNavigationItems = (role)=>{\n        const baseItems = [\n            {\n                name: \"Dashboard\",\n                href: `/dashboard/${role}`,\n                icon: DashboardIcon\n            }\n        ];\n        switch(role){\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.ADMIN:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"User Management\",\n                        href: \"/dashboard/admin/users\",\n                        icon: UsersIcon\n                    },\n                    {\n                        name: \"School Settings\",\n                        href: \"/dashboard/admin/settings\",\n                        icon: CogIcon\n                    },\n                    {\n                        name: \"Academic Setup\",\n                        href: \"/dashboard/admin/academic\",\n                        icon: AcademicIcon\n                    },\n                    {\n                        name: \"Finance Reports\",\n                        href: \"/dashboard/admin/finance\",\n                        icon: ChartIcon\n                    }\n                ];\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.PROPRIETOR:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"Academic Overview\",\n                        href: \"/dashboard/proprietor/academic\",\n                        icon: AcademicIcon\n                    },\n                    {\n                        name: \"Student Performance\",\n                        href: \"/dashboard/proprietor/performance\",\n                        icon: ChartIcon\n                    },\n                    {\n                        name: \"Staff Management\",\n                        href: \"/dashboard/proprietor/staff\",\n                        icon: UsersIcon\n                    },\n                    {\n                        name: \"Finance Summary\",\n                        href: \"/dashboard/proprietor/finance\",\n                        icon: ChartIcon\n                    }\n                ];\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.TEACHER:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"My Classes\",\n                        href: \"/dashboard/teacher/classes\",\n                        icon: AcademicIcon\n                    },\n                    {\n                        name: \"Attendance\",\n                        href: \"/dashboard/teacher/attendance\",\n                        icon: ChartIcon\n                    },\n                    {\n                        name: \"Gradebook\",\n                        href: \"/dashboard/teacher/grades\",\n                        icon: ChartIcon\n                    },\n                    {\n                        name: \"E-Classroom\",\n                        href: \"/dashboard/teacher/classroom\",\n                        icon: AcademicIcon\n                    }\n                ];\n            case _lib_auth__WEBPACK_IMPORTED_MODULE_5__.AUTH_ROLES.STUDENT:\n                return [\n                    ...baseItems,\n                    {\n                        name: \"My Grades\",\n                        href: \"/dashboard/student/grades\",\n                        icon: ChartIcon\n                    },\n                    {\n                        name: \"Attendance\",\n                        href: \"/dashboard/student/attendance\",\n                        icon: ChartIcon\n                    },\n                    {\n                        name: \"Assignments\",\n                        href: \"/dashboard/student/assignments\",\n                        icon: AcademicIcon\n                    },\n                    {\n                        name: \"E-Learning\",\n                        href: \"/dashboard/student/learning\",\n                        icon: AcademicIcon\n                    }\n                ];\n            default:\n                return baseItems;\n        }\n    };\n    const navigationItems = user ? getNavigationItems(user.role) : [];\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        children: [\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-16 px-4 border-b border-gray-200 flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: \"/GHC.jpg\",\n                                    alt: \"Great Heritage College\",\n                                    width: 36,\n                                    height: 36,\n                                    className: \"rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary font-bold text-lg block\",\n                                            children: \"GHC Portal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 capitalize\",\n                                            children: [\n                                                user?.role,\n                                                \" Panel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 px-4 py-6 overflow-y-auto\",\n                        children: navigationItems.map((item)=>{\n                            const IconComponent = item.icon;\n                            const isActive = router.pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: item.href,\n                                className: `flex items-center px-4 py-3 mb-2 rounded-lg transition-all duration-200 ${isActive ? \"bg-primary text-white shadow-md\" : \"text-gray-700 hover:bg-gray-100 hover:text-primary\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-3 font-medium\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, item.name, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white border-b border-gray-200 shadow-sm flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                            className: \"lg:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        \"Good \",\n                                                        new Date().getHours() < 12 ? \"Morning\" : new Date().getHours() < 18 ? \"Afternoon\" : \"Evening\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        user.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12M1.01 11.88l3.12 3.12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 px-3 py-2 rounded-lg bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-primary rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-semibold text-sm\",\n                                                        children: user.name.charAt(0).toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 capitalize\",\n                                                            children: user.role\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"p-2 rounded-lg text-gray-600 hover:text-red-600 hover:bg-red-50 transition-colors\",\n                                            title: \"Logout\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-6 bg-gray-50 overflow-y-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\components\\\\DashboardLayout.js\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/DashboardLayout.js\n");

/***/ }),

/***/ "./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_ROLES: () => (/* binding */ AUTH_ROLES),\n/* harmony export */   ROLE_HIERARCHY: () => (/* binding */ ROLE_HIERARCHY),\n/* harmony export */   authenticatedRequest: () => (/* binding */ authenticatedRequest),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getDashboardRoute: () => (/* binding */ getDashboardRoute),\n/* harmony export */   getToken: () => (/* binding */ getToken),\n/* harmony export */   hasMinimumRole: () => (/* binding */ hasMinimumRole),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Authentication utilities\nconst AUTH_ROLES = {\n    ADMIN: \"admin\",\n    PROPRIETOR: \"proprietor\",\n    TEACHER: \"teacher\",\n    STUDENT: \"student\"\n};\n// Check if user is authenticated\nconst isAuthenticated = ()=>{\n    if (true) return false;\n    const token = localStorage.getItem(\"token\");\n    if (!token) return false;\n    try {\n        const decoded = JSON.parse(Buffer.from(token, \"base64\").toString());\n        return decoded.exp > Date.now();\n    } catch (error) {\n        return false;\n    }\n};\n// Get current user from token\nconst getCurrentUser = ()=>{\n    if (true) return null;\n    const userStr = localStorage.getItem(\"user\");\n    if (!userStr) return null;\n    try {\n        return JSON.parse(userStr);\n    } catch (error) {\n        return null;\n    }\n};\n// Get token from localStorage\nconst getToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"token\");\n};\n// Logout user\nconst logout = ()=>{\n    if (true) return;\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    window.location.href = \"/login\";\n};\n// Check if user has required role\nconst hasRole = (requiredRole)=>{\n    const user = getCurrentUser();\n    if (!user) return false;\n    // Admin has access to everything\n    if (user.role === AUTH_ROLES.ADMIN) return true;\n    // Check specific role\n    return user.role === requiredRole;\n};\n// Role hierarchy for permissions\nconst ROLE_HIERARCHY = {\n    [AUTH_ROLES.ADMIN]: 4,\n    [AUTH_ROLES.PROPRIETOR]: 3,\n    [AUTH_ROLES.TEACHER]: 2,\n    [AUTH_ROLES.STUDENT]: 1\n};\n// Check if user has minimum role level\nconst hasMinimumRole = (minimumRole)=>{\n    const user = getCurrentUser();\n    if (!user) return false;\n    const userLevel = ROLE_HIERARCHY[user.role] || 0;\n    const requiredLevel = ROLE_HIERARCHY[minimumRole] || 0;\n    return userLevel >= requiredLevel;\n};\n// API request with authentication\nconst authenticatedRequest = async (url, options = {})=>{\n    const token = getToken();\n    const defaultOptions = {\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...token && {\n                Authorization: `Bearer ${token}`\n            },\n            ...options.headers\n        }\n    };\n    const response = await fetch(url, {\n        ...options,\n        ...defaultOptions\n    });\n    // Handle unauthorized responses\n    if (response.status === 401) {\n        logout();\n        throw new Error(\"Unauthorized\");\n    }\n    return response;\n};\n// Higher-order component for protecting routes\nconst withAuth = (WrappedComponent, requiredRole = null)=>{\n    return function AuthenticatedComponent(props) {\n        const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n        const [isAuthorized, setIsAuthorized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            const checkAuth = ()=>{\n                if (!isAuthenticated()) {\n                    router.push(\"/login\");\n                    return;\n                }\n                if (requiredRole && !hasRole(requiredRole)) {\n                    router.push(\"/unauthorized\");\n                    return;\n                }\n                setIsAuthorized(true);\n                setIsLoading(false);\n            };\n            checkAuth();\n        }, [\n            router\n        ]);\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\lib\\\\auth.js\",\n                    lineNumber: 139,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\lib\\\\auth.js\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthorized) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\lib\\\\auth.js\",\n            lineNumber: 148,\n            columnNumber: 12\n        }, this);\n    };\n};\n// Dashboard route mapping\nconst getDashboardRoute = (role)=>{\n    const routes = {\n        [AUTH_ROLES.ADMIN]: \"/dashboard/admin\",\n        [AUTH_ROLES.PROPRIETOR]: \"/dashboard/proprietor\",\n        [AUTH_ROLES.TEACHER]: \"/dashboard/teacher\",\n        [AUTH_ROLES.STUDENT]: \"/dashboard/student\"\n    };\n    return routes[role] || \"/dashboard\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/auth.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE4QjtBQUVmLFNBQVNBLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDbEQscUJBQU8sOERBQUNEO1FBQVcsR0FBR0MsU0FBUzs7Ozs7O0FBQ2pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3JlYXQtaGVyaXRhZ2UtY29sbGVnZS1mcm9udGVuZC8uL3BhZ2VzL19hcHAuanM/ZTBhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxufSJdLCJuYW1lcyI6WyJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/dashboard/teacher/classes.js":
/*!********************************************!*\
  !*** ./pages/dashboard/teacher/classes.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherClasses)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/DashboardLayout */ \"./components/DashboardLayout.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../lib/auth */ \"./lib/auth.js\");\n\n\n\n\n\n// Professional SVG Icons\nconst AcademicCapIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l9-5-9-5-9 5 9 5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                lineNumber: 9,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                lineNumber: 10,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined);\nconst UsersIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n            lineNumber: 16,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n        lineNumber: 15,\n        columnNumber: 3\n    }, undefined);\nconst ChartBarIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n            lineNumber: 22,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined);\nconst CalendarIcon = ({ className })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n            lineNumber: 28,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\nfunction TeacherClasses() {\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const user = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.getCurrentUser)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.isAuthenticated)() || !(0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.hasRole)(_lib_auth__WEBPACK_IMPORTED_MODULE_4__.AUTH_ROLES.TEACHER)) {\n            router.push(\"/login\");\n            return;\n        }\n        loadClasses();\n    }, [\n        router\n    ]);\n    const loadClasses = async ()=>{\n        try {\n            // Mock data - replace with actual API calls\n            const mockClasses = [\n                {\n                    id: 1,\n                    name: \"SS1A\",\n                    subject: \"Mathematics\",\n                    students: 35,\n                    schedule: [\n                        {\n                            day: \"Monday\",\n                            time: \"8:00 AM - 9:00 AM\"\n                        },\n                        {\n                            day: \"Wednesday\",\n                            time: \"10:00 AM - 11:00 AM\"\n                        },\n                        {\n                            day: \"Friday\",\n                            time: \"2:00 PM - 3:00 PM\"\n                        }\n                    ],\n                    averageScore: 78.5,\n                    attendanceRate: 92.3,\n                    nextClass: \"2024-12-16 08:00\",\n                    recentTopics: [\n                        \"Quadratic Equations\",\n                        \"Factorization\",\n                        \"Graphs of Functions\"\n                    ]\n                },\n                {\n                    id: 2,\n                    name: \"SS2A\",\n                    subject: \"Mathematics\",\n                    students: 32,\n                    schedule: [\n                        {\n                            day: \"Tuesday\",\n                            time: \"9:00 AM - 10:00 AM\"\n                        },\n                        {\n                            day: \"Thursday\",\n                            time: \"11:00 AM - 12:00 PM\"\n                        },\n                        {\n                            day: \"Friday\",\n                            time: \"1:00 PM - 2:00 PM\"\n                        }\n                    ],\n                    averageScore: 82.1,\n                    attendanceRate: 89.7,\n                    nextClass: \"2024-12-17 09:00\",\n                    recentTopics: [\n                        \"Calculus Basics\",\n                        \"Differentiation\",\n                        \"Integration\"\n                    ]\n                },\n                {\n                    id: 3,\n                    name: \"SS3B\",\n                    subject: \"Mathematics\",\n                    students: 28,\n                    schedule: [\n                        {\n                            day: \"Monday\",\n                            time: \"11:00 AM - 12:00 PM\"\n                        },\n                        {\n                            day: \"Wednesday\",\n                            time: \"2:00 PM - 3:00 PM\"\n                        },\n                        {\n                            day: \"Thursday\",\n                            time: \"8:00 AM - 9:00 AM\"\n                        }\n                    ],\n                    averageScore: 85.3,\n                    attendanceRate: 94.1,\n                    nextClass: \"2024-12-16 11:00\",\n                    recentTopics: [\n                        \"Advanced Calculus\",\n                        \"Statistics\",\n                        \"Probability\"\n                    ]\n                }\n            ];\n            setClasses(mockClasses);\n        } catch (error) {\n            console.error(\"Error loading classes:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getNextClassInfo = (nextClass)=>{\n        const nextDate = new Date(nextClass);\n        const now = new Date();\n        const diffHours = Math.ceil((nextDate - now) / (1000 * 60 * 60));\n        if (diffHours < 0) return \"Class passed\";\n        if (diffHours < 24) return `In ${diffHours} hours`;\n        return `In ${Math.ceil(diffHours / 24)} days`;\n    };\n    const getTotalStats = ()=>{\n        const totalStudents = classes.reduce((sum, cls)=>sum + cls.students, 0);\n        const avgScore = classes.reduce((sum, cls)=>sum + cls.averageScore, 0) / classes.length;\n        const avgAttendance = classes.reduce((sum, cls)=>sum + cls.attendanceRate, 0) / classes.length;\n        return {\n            totalClasses: classes.length,\n            totalStudents,\n            avgScore: avgScore.toFixed(1),\n            avgAttendance: avgAttendance.toFixed(1)\n        };\n    };\n    const stats = getTotalStats();\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold mb-2\",\n                                        children: \"My Classes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-100\",\n                                        children: \"Manage your assigned classes and students\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                    className: \"w-12 h-12 text-white opacity-80\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                children: \"Total Classes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: stats.totalClasses\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-blue-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                            className: \"w-8 h-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                children: \"Total Students\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: stats.totalStudents\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UsersIcon, {\n                                            className: \"w-8 h-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                children: \"Avg Performance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: [\n                                                    stats.avgScore,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-purple-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartBarIcon, {\n                                            className: \"w-8 h-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600 mb-1\",\n                                                children: \"Avg Attendance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: [\n                                                    stats.avgAttendance,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-orange-50 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendarIcon, {\n                                            className: \"w-8 h-8 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                    children: classes.map((classData)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                    children: classData.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: classData.subject\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-primary/10 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                                className: \"w-6 h-6 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: classData.students\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Students\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: [\n                                                        classData.averageScore,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Avg Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-3\",\n                                            children: \"Class Schedule\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: classData.schedule.map((schedule, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: schedule.day\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: schedule.time\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-3 bg-blue-50 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-blue-900\",\n                                                children: \"Next Class\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-blue-700\",\n                                                children: getNextClassInfo(classData.nextClass)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-3\",\n                                            children: \"Recent Topics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: classData.recentTopics.slice(0, 3).map((topic, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-primary rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        topic\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex-1 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium\",\n                                            children: \"View Details\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium\",\n                                            children: \"Take Attendance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, classData.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-6\",\n                            children: \"Quick Actions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendarIcon, {\n                                            className: \"w-6 h-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: \"Mark Attendance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChartBarIcon, {\n                                            className: \"w-6 h-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: \"Enter Grades\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AcademicCapIcon, {\n                                            className: \"w-6 h-6 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: \"Create Assignment\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UsersIcon, {\n                                            className: \"w-6 h-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: \"View Students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\SecSchoolWebsite\\\\pages\\\\dashboard\\\\teacher\\\\classes.js\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/dashboard/teacher/classes.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard%2Fteacher%2Fclasses&preferredRegion=&absolutePagePath=.%2Fpages%5Cdashboard%5Cteacher%5Cclasses.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();
# Great Heritage College - School Management System

A comprehensive school management website built with Next.js, featuring role-based authentication and dashboards for administrators, proprietors, teachers, and students/parents.

## 🚀 Features

### Authentication System
- **Role-based login** with 4 user types:
  - Administrator
  - Proprietor/Headteacher
  - Teacher
  - Student/Parent
- **Secure JWT authentication**
- **Password reset functionality**
- **Session management**

### Dashboard Features
- **Admin Dashboard**: User management, school settings, finance reports
- **Proprietor Dashboard**: Academic overview, performance tracking, staff management
- **Teacher Dashboard**: Class management, attendance, gradebook, e-classroom
- **Student Dashboard**: Grades, assignments, attendance, fee payments

### Core Modules (As per specifications)
1. **User Management** - Role-based access control
2. **Academic Management** - Classes, subjects, grades, report cards
3. **Attendance Management** - Daily marking with parent notifications
4. **E-Classroom** - Lesson notes, assignments, CBT tests
5. **Communication** - Messaging, notifications, live chat
6. **Finance & Payroll** - Fee management, online payments, payroll
7. **Events & Calendar** - Event management with notifications

## 🔐 Test Credentials

### Administrator
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### Proprietor/Principal
- **Email**: `<EMAIL>`
- **Password**: `principal123`

### Teacher
- **Email**: `<EMAIL>`
- **Password**: `teacher123`

### Student
- **Email**: `<EMAIL>`
- **Password**: `student123`

### Parent
- **Email**: `<EMAIL>`
- **Password**: `parent123`

## 🛠️ Getting Started

1. **Clone the repository**
```bash
git clone <repository-url>
cd SecSchoolWebsite
```

2. **Install dependencies**
```bash
npm install
```

3. **Run the development server**
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
├── components/          # Reusable UI components
├── pages/              # Next.js pages/routes
├── styles/             # Global CSS and Tailwind
├── lib/                # API utilities and helpers
├── public/             # Static assets
└── img/                # Images
```

## Content Management

This website is designed to work with a Strapi CMS backend. The CMS allows non-technical staff to:

- Update news and events
- Manage photo gallery
- Edit staff profiles
- Update page content
- Manage admissions information

## Deployment

### Frontend (Vercel)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Backend (Strapi)
Set up Strapi CMS separately with the required content types as outlined in the project documentation.

## Performance Features

- Next.js Image optimization
- Lazy loading
- Static generation where possible
- Tailwind CSS for minimal bundle size
- Responsive images with srcset

## Accessibility

- Semantic HTML structure
- Alt text for all images
- Keyboard navigation support
- Screen reader friendly
- High contrast color scheme

## 📱 How to Use the Login System

1. **Visit the website** - Browse the school's public pages
2. **Click "Login"** - Located in the top navigation bar
3. **Select your role** - Choose from Admin, Proprietor, Teacher, or Student/Parent
4. **Enter credentials** - Use the test credentials provided above
5. **Access your dashboard** - Each role has a customized dashboard with relevant features

## 🔐 Test Credentials

### Administrator
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### Proprietor/Principal
- **Email**: `<EMAIL>`
- **Password**: `principal123`

### Teacher
- **Email**: `<EMAIL>`
- **Password**: `teacher123`

### Student
- **Email**: `<EMAIL>`
- **Password**: `student123`

### Parent
- **Email**: `<EMAIL>`
- **Password**: `parent123`

## 🚀 School Management Features

### Dashboard Features
- **Admin Dashboard**: User management, school settings, finance reports
- **Proprietor Dashboard**: Academic overview, performance tracking, staff management
- **Teacher Dashboard**: Class management, attendance, gradebook, e-classroom
- **Student Dashboard**: Grades, assignments, attendance, fee payments

### Core Modules (As per specifications)
1. **User Management** - Role-based access control
2. **Academic Management** - Classes, subjects, grades, report cards
3. **Attendance Management** - Daily marking with parent notifications
4. **E-Classroom** - Lesson notes, assignments, CBT tests
5. **Communication** - Messaging, notifications, live chat
6. **Finance & Payroll** - Fee management, online payments, payroll
7. **Events & Calendar** - Event management with notifications

## 📋 Implementation Status

The current implementation provides a solid foundation with:
- ✅ Complete authentication system
- ✅ Role-based dashboards
- ✅ Responsive design
- ✅ Mock data for testing

### Future Development
- Database integration (PostgreSQL/MySQL)
- Real API endpoints
- File upload functionality
- Payment gateway integration
- SMS/Email notifications
- Advanced reporting features

---

**Great Heritage College** - *Godliness, Knowledge, and Progress*
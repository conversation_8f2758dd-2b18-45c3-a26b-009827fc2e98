import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import Layout from '../components/Layout'

export default function ForgotPassword() {
  const [email, setEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setMessage('')

    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      if (response.ok) {
        setMessage('Password reset instructions have been sent to your email address.')
      } else {
        const errorData = await response.json()
        setError(errorData.message || 'Failed to send reset email. Please try again.')
      }
    } catch (error) {
      console.error('Forgot password error:', error)
      setError('Network error. Please check your connection and try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-primary/5 via-white to-accent/5 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center">
            <Link href="/" className="inline-flex items-center space-x-3 group mb-8">
              <Image 
                src="/GHC.jpg" 
                alt="Great Heritage College" 
                width={60}
                height={60}
                className="rounded-full transition-transform duration-300 group-hover:scale-105" 
              />
              <span className="text-2xl font-bold text-primary">Great Heritage College</span>
            </Link>
            <h2 className="text-3xl font-bold text-dark mb-2">Forgot Password?</h2>
            <p className="text-gray-600">Enter your email address and we'll send you instructions to reset your password.</p>
          </div>

          {/* Form */}
          <div className="card p-8">
            <form className="space-y-6" onSubmit={handleSubmit}>
              {/* Email Input */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-dark mb-2">
                  Email Address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary outline-none transition-colors duration-300"
                  placeholder="Enter your email address"
                />
              </div>

              {/* Success Message */}
              {message && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg text-sm">
                  {message}
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isLoading}
                className={`w-full btn btn-primary ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Sending...
                  </div>
                ) : (
                  'Send Reset Instructions'
                )}
              </button>

              {/* Back to Login */}
              <div className="text-center">
                <Link 
                  href="/login" 
                  className="text-primary hover:text-primary/80 text-sm font-medium transition-colors duration-300"
                >
                  ← Back to Login
                </Link>
              </div>
            </form>
          </div>

          {/* Footer */}
          <div className="text-center text-sm text-gray-600">
            <p>
              Need help? Contact our support team at{' '}
              <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </Layout>
  )
}

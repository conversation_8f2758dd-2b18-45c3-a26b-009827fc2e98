import { useState } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import Image from 'next/image'
import Layout from '../components/Layout'

export default function Login() {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    setError('')
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const data = await response.json()
        // Store token and user data
        localStorage.setItem('token', data.token)
        localStorage.setItem('user', JSON.stringify(data.user))

        // Redirect based on role
        const dashboardRoutes = {
          admin: '/dashboard/admin',
          proprietor: '/dashboard/proprietor',
          teacher: '/dashboard/teacher',
          student: '/dashboard/student'
        }

        router.push(dashboardRoutes[data.user.role] || '/dashboard')
      } else {
        const errorData = await response.json()
        setError(errorData.message || 'Invalid username or password.')
      }
    } catch (error) {
      console.error('Login error:', error)
      setError('Network error. Please check your connection and try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-primary/5 via-white to-accent/5 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center">
            <Link href="/" className="inline-flex items-center space-x-3 group mb-8">
              <Image 
                src="/GHC.jpg" 
                alt="Great Heritage College" 
                width={60}
                height={60}
                className="rounded-full transition-transform duration-300 group-hover:scale-105" 
              />
              <span className="text-2xl font-bold text-primary">Great Heritage College</span>
            </Link>
            <h2 className="text-3xl font-bold text-dark mb-2">Student & Staff Portal</h2>
            <p className="text-gray-600">Sign in to access your account</p>
          </div>

          {/* Login Form */}
          <div className="card p-8">
            <form className="space-y-6" onSubmit={handleSubmit}>
              {/* Username Input */}
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-dark mb-2">
                  Username
                </label>
                <input
                  id="username"
                  name="username"
                  type="text"
                  autoComplete="username"
                  required
                  value={formData.username}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary outline-none transition-colors duration-300"
                  placeholder="Enter your username"
                />
              </div>

              {/* Password Input */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-dark mb-2">
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary outline-none transition-colors duration-300"
                  placeholder="Enter your password"
                />
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isLoading}
                className={`w-full btn btn-primary ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Signing In...
                  </div>
                ) : (
                  'Sign In'
                )}
              </button>

              {/* Forgot Password */}
              <div className="text-center">
                <Link 
                  href="/forgot-password" 
                  className="text-primary hover:text-primary/80 text-sm font-medium transition-colors duration-300"
                >
                  Forgot your password?
                </Link>
              </div>
            </form>
          </div>

          {/* Footer */}
          <div className="text-center text-sm text-gray-600">
            <p>
              Need help? Contact our support team at{' '}
              <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                <EMAIL>
              </a>
            </p>
            <p className="mt-2">
              <Link href="/" className="text-primary hover:underline">
                ← Back to Website
              </Link>
            </p>
          </div>
        </div>
      </div>
    </Layout>
  )
}

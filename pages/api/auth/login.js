// Mock authentication endpoint - Replace with actual database integration
export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  const { email, password, role } = req.body

  // Validate input
  if (!email || !password || !role) {
    return res.status(400).json({ message: 'Email, password, and role are required' })
  }

  // Mock user database - Replace with actual database queries
  const mockUsers = {
    admin: [
      {
        id: 1,
        email: '<EMAIL>',
        password: 'admin123', // In production, use hashed passwords
        name: 'System Administrator',
        role: 'admin'
      }
    ],
    proprietor: [
      {
        id: 2,
        email: '<EMAIL>',
        password: 'principal123',
        name: 'Dr. <PERSON>',
        role: 'proprietor'
      }
    ],
    teacher: [
      {
        id: 3,
        email: '<EMAIL>',
        password: 'teacher123',
        name: '<PERSON>',
        role: 'teacher',
        subjects: ['Mathematics', 'Physics'],
        classes: ['SS1A', 'SS2B']
      }
    ],
    student: [
      {
        id: 4,
        email: '<EMAIL>',
        password: 'student123',
        name: '<PERSON>',
        role: 'student',
        class: 'SS2A',
        parentEmail: '<EMAIL>'
      },
      {
        id: 5,
        email: '<EMAIL>',
        password: 'parent123',
        name: 'Mrs. Johnson',
        role: 'student', // Parents use student role but have parent privileges
        isParent: true,
        children: [
          { id: 4, name: 'Michael Johnson', class: 'SS2A' }
        ]
      }
    ]
  }

  // Find user in mock database
  const users = mockUsers[role] || []
  const user = users.find(u => u.email === email && u.password === password)

  if (!user) {
    return res.status(401).json({ message: 'Invalid credentials' })
  }

  // Generate mock JWT token (in production, use proper JWT library)
  const token = Buffer.from(JSON.stringify({
    userId: user.id,
    email: user.email,
    role: user.role,
    exp: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
  })).toString('base64')

  // Return user data (excluding password)
  const { password: _, ...userWithoutPassword } = user
  
  res.status(200).json({
    message: 'Login successful',
    token,
    user: userWithoutPassword
  })
}

// Authentication endpoint with environment-based admin and mock users
export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  const { username, password } = req.body

  // Validate input
  if (!username || !password) {
    return res.status(400).json({ message: 'Username and password are required' })
  }

  // Check for admin credentials from environment
  if (username === process.env.ADMIN_USERNAME && password === process.env.ADMIN_PASSWORD) {
    const adminUser = {
      id: 1,
      username: process.env.ADMIN_USERNAME,
      name: 'System Administrator',
      role: 'admin',
      email: '<EMAIL>'
    }

    // Generate token
    const token = Buffer.from(JSON.stringify({
      userId: adminUser.id,
      username: adminUser.username,
      role: adminUser.role,
      exp: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
    })).toString('base64')

    return res.status(200).json({
      message: 'Login successful',
      token,
      user: adminUser
    })
  }

  // Mock user database - Replace with actual database queries
  const mockUsers = [
    {
      id: 2,
      username: 'principal',
      password: 'principal123',
      name: 'Dr. <PERSON>',
      role: 'proprietor',
      email: '<EMAIL>'
    },
    {
      id: 3,
      username: 'jsmith',
      password: 'teacher123',
      name: 'John Smith',
      role: 'teacher',
      email: '<EMAIL>',
      subjects: ['Mathematics', 'Physics'],
      classes: ['SS1A', 'SS2B']
    },
    {
      id: 4,
      username: 'mjohnson',
      password: 'student123',
      name: 'Michael Johnson',
      role: 'student',
      email: '<EMAIL>',
      class: 'SS2A',
      studentId: 'GHC/2024/001'
    },
    {
      id: 5,
      username: 'parent001',
      password: 'parent123',
      name: 'Mrs. Johnson',
      role: 'student',
      email: '<EMAIL>',
      isParent: true,
      children: [
        { id: 4, name: 'Michael Johnson', class: 'SS2A' }
      ]
    }
  ]

  // Find user in mock database
  const user = mockUsers.find(u => u.username === username && u.password === password)

  if (!user) {
    return res.status(401).json({ message: 'Invalid username or password' })
  }

  // Generate mock JWT token (in production, use proper JWT library)
  const token = Buffer.from(JSON.stringify({
    userId: user.id,
    username: user.username,
    role: user.role,
    exp: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
  })).toString('base64')

  // Return user data (excluding password)
  const { password: _, ...userWithoutPassword } = user
  
  res.status(200).json({
    message: 'Login successful',
    token,
    user: userWithoutPassword
  })
}

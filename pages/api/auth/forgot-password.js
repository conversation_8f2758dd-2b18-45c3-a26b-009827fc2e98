// Forgot password endpoint - Mock implementation
export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  const { email } = req.body

  // Validate input
  if (!email) {
    return res.status(400).json({ message: 'Email is required' })
  }

  // Mock email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    return res.status(400).json({ message: 'Please enter a valid email address' })
  }

  // In a real implementation, you would:
  // 1. Check if email exists in database
  // 2. Generate a secure reset token
  // 3. Send email with reset link
  // 4. Store token with expiration in database

  // For now, just return success for any valid email
  res.status(200).json({
    message: 'If an account with this email exists, you will receive password reset instructions shortly.'
  })
}

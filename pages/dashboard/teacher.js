import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES, getCurrentUser } from '../../lib/auth'

export default function TeacherDashboard() {
  const [teacherData, setTeacherData] = useState(null)
  const [todaySchedule, setTodaySchedule] = useState([])
  const [recentActivities, setRecentActivities] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Check authentication and authorization
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.TEACHER)) {
      router.push('/login')
      return
    }

    loadDashboardData()
  }, [router])

  const loadDashboardData = async () => {
    try {
      const user = getCurrentUser()
      
      // Mock data - replace with actual API calls
      setTeacherData({
        name: user.name,
        subjects: user.subjects || ['Mathematics', 'Physics'],
        classes: user.classes || ['SS1A', 'SS2B'],
        totalStudents: 73,
        pendingGrades: 12,
        upcomingTests: 3
      })

      setTodaySchedule([
        { time: '8:00 AM', subject: 'Mathematics', class: 'SS1A', room: 'Room 101' },
        { time: '10:00 AM', subject: 'Physics', class: 'SS2B', room: 'Lab 2' },
        { time: '12:00 PM', subject: 'Mathematics', class: 'SS2A', room: 'Room 101' },
        { time: '2:00 PM', subject: 'Physics', class: 'SS1B', room: 'Lab 2' }
      ])

      setRecentActivities([
        { id: 1, type: 'grade', message: 'Graded SS1A Mathematics test', time: '2 hours ago' },
        { id: 2, type: 'attendance', message: 'Marked attendance for SS2B Physics', time: '4 hours ago' },
        { id: 3, type: 'assignment', message: 'Created new assignment for SS1A', time: '1 day ago' },
        { id: 4, type: 'message', message: 'Replied to parent inquiry', time: '2 days ago' }
      ])
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome, {teacherData.name}</h2>
          <p className="text-gray-600">Manage your classes, grades, and student interactions.</p>
          <div className="mt-4 flex flex-wrap gap-2">
            {teacherData.subjects.map((subject) => (
              <span key={subject} className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                {subject}
              </span>
            ))}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <span className="text-2xl">👨‍🎓</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">{teacherData.totalStudents}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-100 text-orange-600">
                <span className="text-2xl">📝</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Grades</p>
                <p className="text-2xl font-bold text-gray-900">{teacherData.pendingGrades}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <span className="text-2xl">📋</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Upcoming Tests</p>
                <p className="text-2xl font-bold text-gray-900">{teacherData.upcomingTests}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Today's Schedule */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Schedule</h3>
          <div className="space-y-3">
            {todaySchedule.map((schedule, index) => (
              <div key={index} className="flex items-center p-3 rounded-lg bg-gray-50">
                <div className="flex-shrink-0 w-20 text-sm font-medium text-gray-600">
                  {schedule.time}
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-900">{schedule.subject}</p>
                  <p className="text-xs text-gray-500">{schedule.class} • {schedule.room}</p>
                </div>
                <div className="flex-shrink-0">
                  <a 
                    href={`/dashboard/teacher/classes/${schedule.class.toLowerCase()}`}
                    className="text-primary hover:underline text-sm"
                  >
                    View Class
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Classroom Management</h4>
            <div className="space-y-2">
              <a href="/dashboard/teacher/attendance" className="block text-primary hover:underline">
                Mark Attendance
              </a>
              <a href="/dashboard/teacher/grades" className="block text-primary hover:underline">
                Enter Grades
              </a>
              <a href="/dashboard/teacher/classes" className="block text-primary hover:underline">
                View All Classes
              </a>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">E-Classroom</h4>
            <div className="space-y-2">
              <a href="/dashboard/teacher/classroom" className="block text-primary hover:underline">
                Upload Lesson Notes
              </a>
              <a href="/dashboard/teacher/assignments" className="block text-primary hover:underline">
                Create Assignments
              </a>
              <a href="/dashboard/teacher/tests" className="block text-primary hover:underline">
                Create CBT Tests
              </a>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Communication</h4>
            <div className="space-y-2">
              <a href="/dashboard/teacher/messages" className="block text-primary hover:underline">
                Messages (3 new)
              </a>
              <a href="/dashboard/teacher/announcements" className="block text-primary hover:underline">
                Class Announcements
              </a>
              <a href="/dashboard/teacher/parent-contact" className="block text-primary hover:underline">
                Contact Parents
              </a>
            </div>
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
          <div className="space-y-3">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center p-3 rounded-lg bg-gray-50">
                <div className="flex-shrink-0">
                  <div className={`p-2 rounded-full ${
                    activity.type === 'grade' ? 'bg-green-100 text-green-600' :
                    activity.type === 'attendance' ? 'bg-blue-100 text-blue-600' :
                    activity.type === 'assignment' ? 'bg-purple-100 text-purple-600' :
                    'bg-orange-100 text-orange-600'
                  }`}>
                    <span className="text-sm">
                      {activity.type === 'grade' ? '📊' :
                       activity.type === 'attendance' ? '✅' :
                       activity.type === 'assignment' ? '📝' : '💬'}
                    </span>
                  </div>
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

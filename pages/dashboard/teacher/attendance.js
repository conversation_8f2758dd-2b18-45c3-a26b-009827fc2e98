import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES, getCurrentUser } from '../../../lib/auth'

// Professional SVG Icons
const CheckCircleIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

const XCircleIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

const CalendarIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
)

const SaveIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3-3-3m3-3v12" />
  </svg>
)

export default function TeacherAttendance() {
  const [selectedClass, setSelectedClass] = useState('SS1A')
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [students, setStudents] = useState([])
  const [attendance, setAttendance] = useState({})
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const router = useRouter()
  const user = getCurrentUser()

  useEffect(() => {
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.TEACHER)) {
      router.push('/login')
      return
    }
    loadStudents()
  }, [router, selectedClass])

  const loadStudents = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockStudents = [
        { id: 1, name: 'Michael Johnson', studentId: 'GHC/2024/001', class: 'SS1A' },
        { id: 2, name: 'Sarah Williams', studentId: 'GHC/2024/002', class: 'SS1A' },
        { id: 3, name: 'David Brown', studentId: 'GHC/2024/003', class: 'SS1A' },
        { id: 4, name: 'Emma Davis', studentId: 'GHC/2024/004', class: 'SS1A' },
        { id: 5, name: 'James Wilson', studentId: 'GHC/2024/005', class: 'SS1A' },
        { id: 6, name: 'Olivia Taylor', studentId: 'GHC/2024/006', class: 'SS1A' },
        { id: 7, name: 'William Anderson', studentId: 'GHC/2024/007', class: 'SS1A' },
        { id: 8, name: 'Sophia Thomas', studentId: 'GHC/2024/008', class: 'SS1A' },
        { id: 9, name: 'Benjamin Jackson', studentId: 'GHC/2024/009', class: 'SS1A' },
        { id: 10, name: 'Isabella White', studentId: 'GHC/2024/010', class: 'SS1A' },
        { id: 11, name: 'Lucas Harris', studentId: 'GHC/2024/011', class: 'SS1A' },
        { id: 12, name: 'Mia Martin', studentId: 'GHC/2024/012', class: 'SS1A' },
        { id: 13, name: 'Alexander Thompson', studentId: 'GHC/2024/013', class: 'SS1A' },
        { id: 14, name: 'Charlotte Garcia', studentId: 'GHC/2024/014', class: 'SS1A' },
        { id: 15, name: 'Henry Martinez', studentId: 'GHC/2024/015', class: 'SS1A' }
      ]

      setStudents(mockStudents)
      
      // Initialize attendance with all present by default
      const initialAttendance = {}
      mockStudents.forEach(student => {
        initialAttendance[student.id] = 'present'
      })
      setAttendance(initialAttendance)
    } catch (error) {
      console.error('Error loading students:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAttendanceChange = (studentId, status) => {
    setAttendance(prev => ({
      ...prev,
      [studentId]: status
    }))
  }

  const saveAttendance = async () => {
    setIsSaving(true)
    try {
      // Mock API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Count absent students for notification
      const absentStudents = students.filter(student => attendance[student.id] === 'absent')
      
      alert(`Attendance saved successfully! ${absentStudents.length} absent students. Parent notifications will be sent automatically.`)
    } catch (error) {
      console.error('Error saving attendance:', error)
      alert('Error saving attendance. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const getAttendanceStats = () => {
    const total = students.length
    const present = Object.values(attendance).filter(status => status === 'present').length
    const absent = Object.values(attendance).filter(status => status === 'absent').length
    const late = Object.values(attendance).filter(status => status === 'late').length
    
    return { total, present, absent, late }
  }

  const stats = getAttendanceStats()

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Mark Attendance</h1>
              <p className="text-blue-100">Record student attendance for your classes</p>
            </div>
            <div className="hidden md:block">
              <CalendarIcon className="w-12 h-12 text-white opacity-80" />
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Students</p>
                <p className="text-3xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <CalendarIcon className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Present</p>
                <p className="text-3xl font-bold text-green-600">{stats.present}</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <CheckCircleIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Absent</p>
                <p className="text-3xl font-bold text-red-600">{stats.absent}</p>
              </div>
              <div className="p-3 bg-red-50 rounded-lg">
                <XCircleIcon className="w-8 h-8 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Late</p>
                <p className="text-3xl font-bold text-yellow-600">{stats.late}</p>
              </div>
              <div className="p-3 bg-yellow-50 rounded-lg">
                <CalendarIcon className="w-8 h-8 text-yellow-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Select Class</label>
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                >
                  <option value="SS1A">SS1A</option>
                  <option value="SS1B">SS1B</option>
                  <option value="SS2A">SS2A</option>
                  <option value="SS2B">SS2B</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                />
              </div>
            </div>
            <button
              onClick={saveAttendance}
              disabled={isSaving}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors ${
                isSaving 
                  ? 'bg-gray-400 cursor-not-allowed' 
                  : 'bg-primary hover:bg-primary/90'
              } text-white`}
            >
              <SaveIcon className="w-5 h-5" />
              <span>{isSaving ? 'Saving...' : 'Save Attendance'}</span>
            </button>
          </div>
        </div>

        {/* Attendance List */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="px-8 py-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900">
              {selectedClass} - {new Date(selectedDate).toLocaleDateString()}
            </h3>
            <p className="text-sm text-gray-600 mt-1">Mark attendance for each student</p>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {students.map((student) => (
                <div key={student.id} className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-medium text-gray-900">{student.name}</h4>
                      <p className="text-sm text-gray-500">{student.studentId}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleAttendanceChange(student.id, 'present')}
                      className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                        attendance[student.id] === 'present'
                          ? 'bg-green-100 text-green-800 border-2 border-green-300'
                          : 'bg-gray-100 text-gray-600 hover:bg-green-50'
                      }`}
                    >
                      Present
                    </button>
                    <button
                      onClick={() => handleAttendanceChange(student.id, 'absent')}
                      className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                        attendance[student.id] === 'absent'
                          ? 'bg-red-100 text-red-800 border-2 border-red-300'
                          : 'bg-gray-100 text-gray-600 hover:bg-red-50'
                      }`}
                    >
                      Absent
                    </button>
                    <button
                      onClick={() => handleAttendanceChange(student.id, 'late')}
                      className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                        attendance[student.id] === 'late'
                          ? 'bg-yellow-100 text-yellow-800 border-2 border-yellow-300'
                          : 'bg-gray-100 text-gray-600 hover:bg-yellow-50'
                      }`}
                    >
                      Late
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => {
                const newAttendance = {}
                students.forEach(student => {
                  newAttendance[student.id] = 'present'
                })
                setAttendance(newAttendance)
              }}
              className="px-4 py-2 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors"
            >
              Mark All Present
            </button>
            <button
              onClick={() => {
                const newAttendance = {}
                students.forEach(student => {
                  newAttendance[student.id] = 'absent'
                })
                setAttendance(newAttendance)
              }}
              className="px-4 py-2 bg-red-100 text-red-800 rounded-lg hover:bg-red-200 transition-colors"
            >
              Mark All Absent
            </button>
            <button
              onClick={() => {
                const newAttendance = {}
                students.forEach(student => {
                  newAttendance[student.id] = 'present'
                })
                setAttendance(newAttendance)
              }}
              className="px-4 py-2 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors"
            >
              Reset to Default
            </button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

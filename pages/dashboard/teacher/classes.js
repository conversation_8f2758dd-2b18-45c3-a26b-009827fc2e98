import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES, getCurrentUser } from '../../../lib/auth'

// Professional SVG Icons
const AcademicCapIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
  </svg>
)

const UsersIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
  </svg>
)

const ChartBarIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
)

const CalendarIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
)

export default function TeacherClasses() {
  const [classes, setClasses] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const user = getCurrentUser()

  useEffect(() => {
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.TEACHER)) {
      router.push('/login')
      return
    }
    loadClasses()
  }, [router])

  const loadClasses = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockClasses = [
        {
          id: 1,
          name: 'SS1A',
          subject: 'Mathematics',
          students: 35,
          schedule: [
            { day: 'Monday', time: '8:00 AM - 9:00 AM' },
            { day: 'Wednesday', time: '10:00 AM - 11:00 AM' },
            { day: 'Friday', time: '2:00 PM - 3:00 PM' }
          ],
          averageScore: 78.5,
          attendanceRate: 92.3,
          nextClass: '2024-12-16 08:00',
          recentTopics: ['Quadratic Equations', 'Factorization', 'Graphs of Functions']
        },
        {
          id: 2,
          name: 'SS2A',
          subject: 'Mathematics',
          students: 32,
          schedule: [
            { day: 'Tuesday', time: '9:00 AM - 10:00 AM' },
            { day: 'Thursday', time: '11:00 AM - 12:00 PM' },
            { day: 'Friday', time: '1:00 PM - 2:00 PM' }
          ],
          averageScore: 82.1,
          attendanceRate: 89.7,
          nextClass: '2024-12-17 09:00',
          recentTopics: ['Calculus Basics', 'Differentiation', 'Integration']
        },
        {
          id: 3,
          name: 'SS3B',
          subject: 'Mathematics',
          students: 28,
          schedule: [
            { day: 'Monday', time: '11:00 AM - 12:00 PM' },
            { day: 'Wednesday', time: '2:00 PM - 3:00 PM' },
            { day: 'Thursday', time: '8:00 AM - 9:00 AM' }
          ],
          averageScore: 85.3,
          attendanceRate: 94.1,
          nextClass: '2024-12-16 11:00',
          recentTopics: ['Advanced Calculus', 'Statistics', 'Probability']
        }
      ]
      setClasses(mockClasses)
    } catch (error) {
      console.error('Error loading classes:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getNextClassInfo = (nextClass) => {
    const nextDate = new Date(nextClass)
    const now = new Date()
    const diffHours = Math.ceil((nextDate - now) / (1000 * 60 * 60))
    
    if (diffHours < 0) return 'Class passed'
    if (diffHours < 24) return `In ${diffHours} hours`
    return `In ${Math.ceil(diffHours / 24)} days`
  }

  const getTotalStats = () => {
    const totalStudents = classes.reduce((sum, cls) => sum + cls.students, 0)
    const avgScore = classes.reduce((sum, cls) => sum + cls.averageScore, 0) / classes.length
    const avgAttendance = classes.reduce((sum, cls) => sum + cls.attendanceRate, 0) / classes.length
    
    return {
      totalClasses: classes.length,
      totalStudents,
      avgScore: avgScore.toFixed(1),
      avgAttendance: avgAttendance.toFixed(1)
    }
  }

  const stats = getTotalStats()

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">My Classes</h1>
              <p className="text-blue-100">Manage your assigned classes and students</p>
            </div>
            <div className="hidden md:block">
              <AcademicCapIcon className="w-12 h-12 text-white opacity-80" />
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Classes</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalClasses}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <AcademicCapIcon className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Students</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalStudents}</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <UsersIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Avg Performance</p>
                <p className="text-3xl font-bold text-gray-900">{stats.avgScore}%</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <ChartBarIcon className="w-8 h-8 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Avg Attendance</p>
                <p className="text-3xl font-bold text-gray-900">{stats.avgAttendance}%</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <CalendarIcon className="w-8 h-8 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Classes Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {classes.map((classData) => (
            <div key={classData.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">{classData.name}</h3>
                  <p className="text-sm text-gray-600">{classData.subject}</p>
                </div>
                <div className="p-2 bg-primary/10 rounded-lg">
                  <AcademicCapIcon className="w-6 h-6 text-primary" />
                </div>
              </div>

              {/* Class Stats */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">{classData.students}</p>
                  <p className="text-xs text-gray-600">Students</p>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">{classData.averageScore}%</p>
                  <p className="text-xs text-gray-600">Avg Score</p>
                </div>
              </div>

              {/* Schedule */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-3">Class Schedule</h4>
                <div className="space-y-2">
                  {classData.schedule.map((schedule, index) => (
                    <div key={index} className="flex justify-between items-center text-sm">
                      <span className="font-medium text-gray-700">{schedule.day}</span>
                      <span className="text-gray-600">{schedule.time}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Next Class */}
              <div className="mb-6 p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-blue-900">Next Class</span>
                  <span className="text-sm text-blue-700">{getNextClassInfo(classData.nextClass)}</span>
                </div>
              </div>

              {/* Recent Topics */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-3">Recent Topics</h4>
                <div className="space-y-1">
                  {classData.recentTopics.slice(0, 3).map((topic, index) => (
                    <div key={index} className="text-sm text-gray-600 flex items-center">
                      <div className="w-2 h-2 bg-primary rounded-full mr-2"></div>
                      {topic}
                    </div>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <button className="flex-1 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium">
                  View Details
                </button>
                <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">
                  Take Attendance
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <CalendarIcon className="w-6 h-6 text-blue-600" />
              <span className="font-medium text-gray-900">Mark Attendance</span>
            </button>
            <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <ChartBarIcon className="w-6 h-6 text-green-600" />
              <span className="font-medium text-gray-900">Enter Grades</span>
            </button>
            <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <AcademicCapIcon className="w-6 h-6 text-purple-600" />
              <span className="font-medium text-gray-900">Create Assignment</span>
            </button>
            <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <UsersIcon className="w-6 h-6 text-orange-600" />
              <span className="font-medium text-gray-900">View Students</span>
            </button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

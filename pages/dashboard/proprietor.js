import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES } from '../../lib/auth'

export default function ProprietorDashboard() {
  const [stats, setStats] = useState({
    totalRevenue: 0,
    averageGrade: 0,
    attendanceRate: 0,
    upcomingEvents: 0
  })
  const [performanceData, setPerformanceData] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Check authentication and authorization
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.PROPRIETOR)) {
      router.push('/login')
      return
    }

    loadDashboardData()
  }, [router])

  const loadDashboardData = async () => {
    try {
      // Mock data - replace with actual API calls
      setStats({
        totalRevenue: 2450000,
        averageGrade: 78.5,
        attendanceRate: 94.2,
        upcomingEvents: 5
      })

      setPerformanceData([
        { class: 'SS3A', students: 35, avgGrade: 82.3, attendance: 96.1 },
        { class: 'SS3B', students: 33, avgGrade: 79.8, attendance: 94.5 },
        { class: 'SS2A', students: 38, avgGrade: 76.2, attendance: 93.8 },
        { class: 'SS2B', students: 36, avgGrade: 74.9, attendance: 92.3 },
        { class: 'SS1A', students: 40, avgGrade: 71.5, attendance: 95.2 }
      ])
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount)
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome, Principal</h2>
          <p className="text-gray-600">Monitor school performance and manage operations from your dashboard.</p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <span className="text-2xl">💰</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-xl font-bold text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Average Grade</p>
                <p className="text-xl font-bold text-gray-900">{stats.averageGrade}%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                <span className="text-2xl">✅</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Attendance Rate</p>
                <p className="text-xl font-bold text-gray-900">{stats.attendanceRate}%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-100 text-orange-600">
                <span className="text-2xl">📅</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Upcoming Events</p>
                <p className="text-xl font-bold text-gray-900">{stats.upcomingEvents}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Class Performance Overview */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Class Performance Overview</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Class
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Students
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Avg Grade
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Attendance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {performanceData.map((classData) => (
                  <tr key={classData.class}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {classData.class}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {classData.students}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {classData.avgGrade}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {classData.attendance}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        classData.avgGrade >= 80 ? 'bg-green-100 text-green-800' :
                        classData.avgGrade >= 70 ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {classData.avgGrade >= 80 ? 'Excellent' :
                         classData.avgGrade >= 70 ? 'Good' : 'Needs Improvement'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Academic Management</h4>
            <div className="space-y-2">
              <a href="/dashboard/proprietor/performance" className="block text-primary hover:underline">
                View Detailed Performance Reports
              </a>
              <a href="/dashboard/proprietor/academic" className="block text-primary hover:underline">
                Manage Academic Sessions
              </a>
              <a href="/dashboard/proprietor/staff" className="block text-primary hover:underline">
                Staff Performance Review
              </a>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Communications</h4>
            <div className="space-y-2">
              <a href="/dashboard/proprietor/communications" className="block text-primary hover:underline">
                Send Bulk Messages
              </a>
              <a href="/dashboard/proprietor/events" className="block text-primary hover:underline">
                Manage School Events
              </a>
              <a href="/dashboard/proprietor/announcements" className="block text-primary hover:underline">
                Create Announcements
              </a>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Financial Overview</h4>
            <div className="space-y-2">
              <a href="/dashboard/proprietor/finance" className="block text-primary hover:underline">
                View Financial Reports
              </a>
              <a href="/dashboard/proprietor/payroll" className="block text-primary hover:underline">
                Manage Payroll
              </a>
              <a href="/dashboard/proprietor/fees" className="block text-primary hover:underline">
                Fee Collection Status
              </a>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

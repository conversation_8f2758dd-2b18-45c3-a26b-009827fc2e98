import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES } from '../../lib/auth'

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalTeachers: 0,
    totalClasses: 0,
    pendingPayments: 0
  })
  const [recentActivities, setRecentActivities] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Check authentication and authorization
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.ADMIN)) {
      router.push('/login')
      return
    }

    // Load dashboard data
    loadDashboardData()
  }, [router])

  const loadDashboardData = async () => {
    try {
      // Mock data - replace with actual API calls
      setStats({
        totalStudents: 847,
        totalTeachers: 45,
        totalClasses: 24,
        pendingPayments: 23
      })

      setRecentActivities([
        { id: 1, type: 'user', message: 'New teacher <PERSON> registered', time: '2 hours ago' },
        { id: 2, type: 'payment', message: '15 fee payments received', time: '4 hours ago' },
        { id: 3, type: 'system', message: 'Database backup completed', time: '6 hours ago' },
        { id: 4, type: 'academic', message: 'SS3 results published', time: '1 day ago' },
        { id: 5, type: 'communication', message: 'Bulk SMS sent to parents', time: '2 days ago' }
      ])
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const quickActions = [
    { name: 'Add New User', href: '/dashboard/admin/users/new', icon: '👤', color: 'bg-blue-500' },
    { name: 'Manage Classes', href: '/dashboard/admin/academic/classes', icon: '🏫', color: 'bg-green-500' },
    { name: 'View Reports', href: '/dashboard/admin/reports', icon: '📊', color: 'bg-purple-500' },
    { name: 'System Settings', href: '/dashboard/admin/settings', icon: '⚙️', color: 'bg-gray-500' },
    { name: 'Send Notifications', href: '/dashboard/admin/notifications', icon: '📢', color: 'bg-orange-500' },
    { name: 'Backup Data', href: '/dashboard/admin/backup', icon: '💾', color: 'bg-red-500' }
  ]

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome, Administrator</h2>
          <p className="text-gray-600">Here's an overview of your school management system.</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <span className="text-2xl">👨‍🎓</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalStudents}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <span className="text-2xl">👨‍🏫</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Teachers</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalTeachers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                <span className="text-2xl">🏫</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Classes</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalClasses}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-100 text-orange-600">
                <span className="text-2xl">💰</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Payments</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pendingPayments}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {quickActions.map((action) => (
              <a
                key={action.name}
                href={action.href}
                className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200"
              >
                <div className={`p-3 rounded-full ${action.color} text-white mb-2`}>
                  <span className="text-xl">{action.icon}</span>
                </div>
                <span className="text-sm font-medium text-gray-900 text-center">{action.name}</span>
              </a>
            ))}
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center p-3 rounded-lg bg-gray-50">
                <div className="flex-shrink-0">
                  <div className={`p-2 rounded-full ${
                    activity.type === 'user' ? 'bg-blue-100 text-blue-600' :
                    activity.type === 'payment' ? 'bg-green-100 text-green-600' :
                    activity.type === 'system' ? 'bg-gray-100 text-gray-600' :
                    activity.type === 'academic' ? 'bg-purple-100 text-purple-600' :
                    'bg-orange-100 text-orange-600'
                  }`}>
                    <span className="text-sm">
                      {activity.type === 'user' ? '👤' :
                       activity.type === 'payment' ? '💰' :
                       activity.type === 'system' ? '⚙️' :
                       activity.type === 'academic' ? '📚' : '📢'}
                    </span>
                  </div>
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

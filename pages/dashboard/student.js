import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES, getCurrentUser } from '../../lib/auth'

export default function StudentDashboard() {
  const [studentData, setStudentData] = useState(null)
  const [recentGrades, setRecentGrades] = useState([])
  const [upcomingAssignments, setUpcomingAssignments] = useState([])
  const [announcements, setAnnouncements] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Check authentication and authorization
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.STUDENT)) {
      router.push('/login')
      return
    }

    loadDashboardData()
  }, [router])

  const loadDashboardData = async () => {
    try {
      const user = getCurrentUser()
      
      // Mock data - replace with actual API calls
      setStudentData({
        name: user.name,
        class: user.class || 'SS2A',
        studentId: 'GHC/2024/001',
        currentGPA: 3.45,
        attendanceRate: 96.2,
        pendingFees: 45000,
        isParent: user.isParent || false,
        children: user.children || []
      })

      setRecentGrades([
        { subject: 'Mathematics', grade: 'A', score: 85, date: '2024-12-10' },
        { subject: 'Physics', grade: 'B+', score: 78, date: '2024-12-08' },
        { subject: 'Chemistry', grade: 'A-', score: 82, date: '2024-12-05' },
        { subject: 'English', grade: 'B', score: 75, date: '2024-12-03' }
      ])

      setUpcomingAssignments([
        { subject: 'Biology', title: 'Cell Structure Report', dueDate: '2024-12-20', status: 'pending' },
        { subject: 'Mathematics', title: 'Calculus Problem Set', dueDate: '2024-12-18', status: 'pending' },
        { subject: 'Literature', title: 'Essay on Shakespearean Themes', dueDate: '2024-12-22', status: 'submitted' }
      ])

      setAnnouncements([
        { id: 1, title: 'Mid-term Exam Schedule Released', date: '2024-12-12', priority: 'high' },
        { id: 2, title: 'Science Fair Registration Open', date: '2024-12-10', priority: 'medium' },
        { id: 3, title: 'Library Hours Extended', date: '2024-12-08', priority: 'low' }
      ])
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount)
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome, {studentData.name}
            {studentData.isParent && ' (Parent Portal)'}
          </h2>
          <p className="text-gray-600">
            {studentData.isParent 
              ? 'Monitor your child\'s academic progress and school activities.'
              : 'Track your academic progress and stay updated with school activities.'
            }
          </p>
          {!studentData.isParent && (
            <div className="mt-4 flex items-center space-x-4 text-sm text-gray-600">
              <span>Class: <strong>{studentData.class}</strong></span>
              <span>Student ID: <strong>{studentData.studentId}</strong></span>
            </div>
          )}
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Current GPA</p>
                <p className="text-2xl font-bold text-gray-900">{studentData.currentGPA}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <span className="text-2xl">✅</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Attendance</p>
                <p className="text-2xl font-bold text-gray-900">{studentData.attendanceRate}%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-100 text-orange-600">
                <span className="text-2xl">📝</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Assignments</p>
                <p className="text-2xl font-bold text-gray-900">{upcomingAssignments.filter(a => a.status === 'pending').length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-red-100 text-red-600">
                <span className="text-2xl">💰</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Fees</p>
                <p className="text-lg font-bold text-gray-900">{formatCurrency(studentData.pendingFees)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Grades */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Grades</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Subject
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Grade
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentGrades.map((grade, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {grade.subject}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {grade.score}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        grade.score >= 80 ? 'bg-green-100 text-green-800' :
                        grade.score >= 70 ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {grade.grade}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(grade.date).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Upcoming Assignments & Announcements */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Assignments */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Upcoming Assignments</h3>
            <div className="space-y-3">
              {upcomingAssignments.map((assignment, index) => (
                <div key={index} className="flex items-center p-3 rounded-lg bg-gray-50">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{assignment.title}</p>
                    <p className="text-xs text-gray-500">{assignment.subject} • Due: {new Date(assignment.dueDate).toLocaleDateString()}</p>
                  </div>
                  <div className="flex-shrink-0">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      assignment.status === 'submitted' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                    }`}>
                      {assignment.status === 'submitted' ? 'Submitted' : 'Pending'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Announcements */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">School Announcements</h3>
            <div className="space-y-3">
              {announcements.map((announcement) => (
                <div key={announcement.id} className="flex items-start p-3 rounded-lg bg-gray-50">
                  <div className={`flex-shrink-0 w-2 h-2 rounded-full mt-2 mr-3 ${
                    announcement.priority === 'high' ? 'bg-red-500' :
                    announcement.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                  }`}></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{announcement.title}</p>
                    <p className="text-xs text-gray-500">{new Date(announcement.date).toLocaleDateString()}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Academic</h4>
            <div className="space-y-2">
              <a href="/dashboard/student/grades" className="block text-primary hover:underline">
                View All Grades
              </a>
              <a href="/dashboard/student/assignments" className="block text-primary hover:underline">
                Submit Assignments
              </a>
              <a href="/dashboard/student/learning" className="block text-primary hover:underline">
                Access E-Learning
              </a>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">School Life</h4>
            <div className="space-y-2">
              <a href="/dashboard/student/attendance" className="block text-primary hover:underline">
                View Attendance
              </a>
              <a href="/dashboard/student/events" className="block text-primary hover:underline">
                School Events
              </a>
              <a href="/dashboard/student/messages" className="block text-primary hover:underline">
                Messages
              </a>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Financial</h4>
            <div className="space-y-2">
              <a href="/dashboard/student/payments" className="block text-primary hover:underline">
                Pay Fees Online
              </a>
              <a href="/dashboard/student/receipts" className="block text-primary hover:underline">
                Download Receipts
              </a>
              <a href="/dashboard/student/fee-history" className="block text-primary hover:underline">
                Payment History
              </a>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

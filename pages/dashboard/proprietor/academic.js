import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES } from '../../../lib/auth'

// Professional SVG Icons
const AcademicCapIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
  </svg>
)

const ChartBarIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
)

const TrendingUpIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
  </svg>
)

const BookOpenIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
  </svg>
)

export default function ProprietorAcademic() {
  const [academicData, setAcademicData] = useState(null)
  const [selectedTerm, setSelectedTerm] = useState('current')
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.PROPRIETOR)) {
      router.push('/login')
      return
    }
    loadAcademicData()
  }, [router, selectedTerm])

  const loadAcademicData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockData = {
        overview: {
          totalStudents: 450,
          totalTeachers: 28,
          totalSubjects: 12,
          averagePerformance: 78.5,
          attendanceRate: 92.3
        },
        classPerformance: [
          { class: 'SS3A', students: 35, average: 82.5, attendance: 94.2, topSubject: 'Mathematics' },
          { class: 'SS3B', students: 33, average: 79.8, attendance: 91.5, topSubject: 'English' },
          { class: 'SS2A', students: 38, average: 76.2, attendance: 93.1, topSubject: 'Physics' },
          { class: 'SS2B', students: 36, average: 74.8, attendance: 89.7, topSubject: 'Chemistry' },
          { class: 'SS1A', students: 40, average: 71.5, attendance: 95.2, topSubject: 'Biology' },
          { class: 'SS1B', students: 38, average: 69.3, attendance: 92.8, topSubject: 'Geography' }
        ],
        subjectPerformance: [
          { subject: 'Mathematics', average: 75.2, passRate: 85.4, teacher: 'Mr. Johnson' },
          { subject: 'English Language', average: 78.9, passRate: 89.2, teacher: 'Mrs. Smith' },
          { subject: 'Physics', average: 72.1, passRate: 78.6, teacher: 'Dr. Brown' },
          { subject: 'Chemistry', average: 74.5, passRate: 82.1, teacher: 'Mrs. Davis' },
          { subject: 'Biology', average: 79.8, passRate: 91.3, teacher: 'Mr. Wilson' },
          { subject: 'Geography', average: 76.4, passRate: 86.7, teacher: 'Mrs. Taylor' }
        ],
        recentAchievements: [
          { id: 1, title: 'State Mathematics Competition', description: '3 students qualified for finals', date: '2024-12-10', type: 'competition' },
          { id: 2, title: 'Science Fair Winners', description: 'SS2A won regional science fair', date: '2024-12-08', type: 'award' },
          { id: 3, title: 'Debate Championship', description: 'School team reached semi-finals', date: '2024-12-05', type: 'competition' },
          { id: 4, title: 'Academic Excellence', description: '15 students achieved 90%+ average', date: '2024-12-01', type: 'achievement' }
        ]
      }
      setAcademicData(mockData)
    } catch (error) {
      console.error('Error loading academic data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getPerformanceColor = (average) => {
    if (average >= 80) return 'text-green-600'
    if (average >= 70) return 'text-blue-600'
    if (average >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getAchievementIcon = (type) => {
    switch (type) {
      case 'competition':
        return '🏆'
      case 'award':
        return '🥇'
      case 'achievement':
        return '⭐'
      default:
        return '📚'
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Academic Overview</h1>
              <p className="text-blue-100">Monitor school-wide academic performance</p>
            </div>
            <div className="hidden md:block">
              <AcademicCapIcon className="w-12 h-12 text-white opacity-80" />
            </div>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Students</p>
                <p className="text-3xl font-bold text-gray-900">{academicData?.overview.totalStudents}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <AcademicCapIcon className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Teachers</p>
                <p className="text-3xl font-bold text-gray-900">{academicData?.overview.totalTeachers}</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <BookOpenIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Subjects</p>
                <p className="text-3xl font-bold text-gray-900">{academicData?.overview.totalSubjects}</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <BookOpenIcon className="w-8 h-8 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Avg Performance</p>
                <p className="text-3xl font-bold text-gray-900">{academicData?.overview.averagePerformance}%</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <TrendingUpIcon className="w-8 h-8 text-orange-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Attendance Rate</p>
                <p className="text-3xl font-bold text-gray-900">{academicData?.overview.attendanceRate}%</p>
              </div>
              <div className="p-3 bg-indigo-50 rounded-lg">
                <ChartBarIcon className="w-8 h-8 text-indigo-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Term Selection */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-gray-900">Academic Performance Analysis</h3>
            <select
              value={selectedTerm}
              onChange={(e) => setSelectedTerm(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
            >
              <option value="current">Current Term (Second Term)</option>
              <option value="first">First Term</option>
              <option value="third">Third Term</option>
            </select>
          </div>
        </div>

        {/* Class Performance */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="px-8 py-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900">Class Performance</h3>
            <p className="text-sm text-gray-600 mt-1">Performance metrics by class</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average Score</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Top Subject</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {academicData?.classPerformance.map((classData) => (
                  <tr key={classData.class} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{classData.class}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{classData.students}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-lg font-semibold ${getPerformanceColor(classData.average)}`}>
                        {classData.average}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{classData.attendance}%</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{classData.topSubject}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Subject Performance */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="px-8 py-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900">Subject Performance</h3>
            <p className="text-sm text-gray-600 mt-1">Performance metrics by subject</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average Score</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pass Rate</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {academicData?.subjectPerformance.map((subject) => (
                  <tr key={subject.subject} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{subject.subject}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-lg font-semibold ${getPerformanceColor(subject.average)}`}>
                        {subject.average}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subject.passRate}%</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{subject.teacher}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Recent Achievements */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900">Recent Academic Achievements</h3>
          </div>
          <div className="space-y-4">
            {academicData?.recentAchievements.map((achievement) => (
              <div key={achievement.id} className="flex items-center p-4 rounded-lg border border-gray-100 hover:bg-gray-50 transition-colors">
                <div className="text-2xl mr-4">
                  {getAchievementIcon(achievement.type)}
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900">{achievement.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{achievement.description}</p>
                  <p className="text-xs text-gray-500 mt-1">{new Date(achievement.date).toLocaleDateString()}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

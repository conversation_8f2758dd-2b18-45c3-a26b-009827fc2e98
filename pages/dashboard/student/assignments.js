import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES, getCurrentUser } from '../../../lib/auth'

// Professional SVG Icons
const DocumentTextIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
)

const ClockIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

const CheckCircleIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

const UploadIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
  </svg>
)

const DownloadIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
)

export default function StudentAssignments() {
  const [assignments, setAssignments] = useState([])
  const [filteredAssignments, setFilteredAssignments] = useState([])
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [selectedSubject, setSelectedSubject] = useState('all')
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const user = getCurrentUser()

  useEffect(() => {
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.STUDENT)) {
      router.push('/login')
      return
    }
    loadAssignments()
  }, [router])

  useEffect(() => {
    filterAssignments()
  }, [assignments, selectedFilter, selectedSubject])

  const loadAssignments = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockAssignments = [
        {
          id: 1,
          title: 'Quadratic Equations Practice',
          subject: 'Mathematics',
          teacher: 'Mr. Johnson',
          description: 'Solve the given quadratic equations using different methods',
          dueDate: '2024-12-20',
          assignedDate: '2024-12-10',
          status: 'pending',
          type: 'homework',
          maxScore: 20,
          submittedScore: null,
          attachments: ['quadratic_problems.pdf']
        },
        {
          id: 2,
          title: 'Essay on Climate Change',
          subject: 'English Language',
          teacher: 'Mrs. Smith',
          description: 'Write a 500-word essay on the effects of climate change',
          dueDate: '2024-12-18',
          assignedDate: '2024-12-08',
          status: 'submitted',
          type: 'essay',
          maxScore: 25,
          submittedScore: 22,
          attachments: ['essay_guidelines.pdf'],
          submissionDate: '2024-12-17'
        },
        {
          id: 3,
          title: 'Physics Lab Report',
          subject: 'Physics',
          teacher: 'Dr. Brown',
          description: 'Complete lab report on pendulum motion experiment',
          dueDate: '2024-12-22',
          assignedDate: '2024-12-12',
          status: 'pending',
          type: 'lab_report',
          maxScore: 30,
          submittedScore: null,
          attachments: ['lab_template.docx', 'experiment_data.xlsx']
        },
        {
          id: 4,
          title: 'Chemical Bonding Quiz',
          subject: 'Chemistry',
          teacher: 'Mrs. Davis',
          description: 'Online quiz on ionic and covalent bonding',
          dueDate: '2024-12-16',
          assignedDate: '2024-12-14',
          status: 'overdue',
          type: 'quiz',
          maxScore: 15,
          submittedScore: null,
          attachments: []
        },
        {
          id: 5,
          title: 'Cell Structure Diagram',
          subject: 'Biology',
          teacher: 'Mr. Wilson',
          description: 'Draw and label plant and animal cell structures',
          dueDate: '2024-12-25',
          assignedDate: '2024-12-15',
          status: 'pending',
          type: 'project',
          maxScore: 35,
          submittedScore: null,
          attachments: ['cell_diagram_template.pdf']
        },
        {
          id: 6,
          title: 'World War II Timeline',
          subject: 'History',
          teacher: 'Mr. Anderson',
          description: 'Create a detailed timeline of major WWII events',
          dueDate: '2024-12-14',
          assignedDate: '2024-12-01',
          status: 'graded',
          type: 'project',
          maxScore: 40,
          submittedScore: 35,
          attachments: ['timeline_requirements.pdf'],
          submissionDate: '2024-12-13',
          feedback: 'Good work! Include more details about the Pacific theater.'
        }
      ]
      setAssignments(mockAssignments)
    } catch (error) {
      console.error('Error loading assignments:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filterAssignments = () => {
    let filtered = assignments

    if (selectedFilter !== 'all') {
      filtered = filtered.filter(assignment => assignment.status === selectedFilter)
    }

    if (selectedSubject !== 'all') {
      filtered = filtered.filter(assignment => assignment.subject === selectedSubject)
    }

    setFilteredAssignments(filtered)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'submitted': return 'bg-blue-100 text-blue-800'
      case 'graded': return 'bg-green-100 text-green-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return <ClockIcon className="w-5 h-5" />
      case 'submitted': return <UploadIcon className="w-5 h-5" />
      case 'graded': return <CheckCircleIcon className="w-5 h-5" />
      case 'overdue': return <ClockIcon className="w-5 h-5" />
      default: return <DocumentTextIcon className="w-5 h-5" />
    }
  }

  const getDaysUntilDue = (dueDate) => {
    const today = new Date()
    const due = new Date(dueDate)
    const diffTime = due - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getAssignmentStats = () => {
    const total = assignments.length
    const pending = assignments.filter(a => a.status === 'pending').length
    const submitted = assignments.filter(a => a.status === 'submitted').length
    const graded = assignments.filter(a => a.status === 'graded').length
    const overdue = assignments.filter(a => a.status === 'overdue').length
    
    return { total, pending, submitted, graded, overdue }
  }

  const stats = getAssignmentStats()
  const subjects = [...new Set(assignments.map(a => a.subject))]

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">My Assignments</h1>
              <p className="text-blue-100">Track and submit your assignments</p>
            </div>
            <div className="hidden md:block">
              <DocumentTextIcon className="w-12 h-12 text-white opacity-80" />
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total</p>
                <p className="text-3xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <DocumentTextIcon className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Pending</p>
                <p className="text-3xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <div className="p-3 bg-yellow-50 rounded-lg">
                <ClockIcon className="w-8 h-8 text-yellow-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Submitted</p>
                <p className="text-3xl font-bold text-blue-600">{stats.submitted}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <UploadIcon className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Graded</p>
                <p className="text-3xl font-bold text-green-600">{stats.graded}</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <CheckCircleIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Overdue</p>
                <p className="text-3xl font-bold text-red-600">{stats.overdue}</p>
              </div>
              <div className="p-3 bg-red-50 rounded-lg">
                <ClockIcon className="w-8 h-8 text-red-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Status</label>
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
              >
                <option value="all">All Assignments</option>
                <option value="pending">Pending</option>
                <option value="submitted">Submitted</option>
                <option value="graded">Graded</option>
                <option value="overdue">Overdue</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Subject</label>
              <select
                value={selectedSubject}
                onChange={(e) => setSelectedSubject(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
              >
                <option value="all">All Subjects</option>
                {subjects.map(subject => (
                  <option key={subject} value={subject}>{subject}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Assignments List */}
        <div className="space-y-4">
          {filteredAssignments.map((assignment) => {
            const daysUntilDue = getDaysUntilDue(assignment.dueDate)
            return (
              <div key={assignment.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-xl font-semibold text-gray-900">{assignment.title}</h3>
                      <span className={`inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full ${getStatusColor(assignment.status)}`}>
                        {getStatusIcon(assignment.status)}
                        <span className="ml-1 capitalize">{assignment.status}</span>
                      </span>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                      <span className="font-medium">{assignment.subject}</span>
                      <span>•</span>
                      <span>{assignment.teacher}</span>
                      <span>•</span>
                      <span>Max Score: {assignment.maxScore}</span>
                      {assignment.submittedScore && (
                        <>
                          <span>•</span>
                          <span className="font-semibold text-green-600">Score: {assignment.submittedScore}/{assignment.maxScore}</span>
                        </>
                      )}
                    </div>
                    <p className="text-gray-700 mb-4">{assignment.description}</p>
                    
                    {assignment.attachments.length > 0 && (
                      <div className="mb-4">
                        <p className="text-sm font-medium text-gray-700 mb-2">Attachments:</p>
                        <div className="flex flex-wrap gap-2">
                          {assignment.attachments.map((attachment, index) => (
                            <button
                              key={index}
                              className="flex items-center space-x-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                            >
                              <DownloadIcon className="w-4 h-4" />
                              <span>{attachment}</span>
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    {assignment.feedback && (
                      <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm font-medium text-blue-900 mb-1">Teacher Feedback:</p>
                        <p className="text-sm text-blue-800">{assignment.feedback}</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="ml-6 text-right">
                    <div className="mb-2">
                      <p className="text-sm text-gray-600">Due Date</p>
                      <p className="font-semibold text-gray-900">{new Date(assignment.dueDate).toLocaleDateString()}</p>
                      {assignment.status === 'pending' && (
                        <p className={`text-xs mt-1 ${daysUntilDue < 0 ? 'text-red-600' : daysUntilDue <= 2 ? 'text-orange-600' : 'text-gray-600'}`}>
                          {daysUntilDue < 0 ? `${Math.abs(daysUntilDue)} days overdue` : 
                           daysUntilDue === 0 ? 'Due today' : 
                           `${daysUntilDue} days left`}
                        </p>
                      )}
                    </div>
                    
                    {assignment.status === 'pending' && (
                      <button className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                        Submit Assignment
                      </button>
                    )}
                    
                    {assignment.submissionDate && (
                      <div className="mt-2">
                        <p className="text-xs text-gray-500">Submitted on</p>
                        <p className="text-sm text-gray-700">{new Date(assignment.submissionDate).toLocaleDateString()}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {filteredAssignments.length === 0 && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
            <DocumentTextIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No assignments found</h3>
            <p className="text-gray-600">Try adjusting your filters to see more assignments.</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}

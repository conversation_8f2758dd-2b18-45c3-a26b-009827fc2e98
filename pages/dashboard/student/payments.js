import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES, getCurrentUser } from '../../../lib/auth'

// Professional SVG Icons
const CreditCardIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
  </svg>
)

const CheckCircleIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

const ClockIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

const DownloadIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
)

export default function StudentPayments() {
  const [paymentData, setPaymentData] = useState(null)
  const [paymentHistory, setPaymentHistory] = useState([])
  const [selectedPayment, setSelectedPayment] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const user = getCurrentUser()

  useEffect(() => {
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.STUDENT)) {
      router.push('/login')
      return
    }
    loadPaymentData()
  }, [router])

  const loadPaymentData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockPaymentData = {
        student: {
          name: user?.name || 'Michael Johnson',
          studentId: 'GHC/2024/001',
          class: 'SS2A',
          session: '2023/2024'
        },
        currentTerm: {
          term: 'Second Term',
          tuitionFee: 150000,
          developmentFee: 25000,
          examFee: 15000,
          sportsFee: 10000,
          totalFee: 200000,
          amountPaid: 120000,
          balance: 80000,
          dueDate: '2024-01-30',
          status: 'partial'
        },
        summary: {
          totalFeesThisSession: 600000,
          totalPaidThisSession: 420000,
          outstandingBalance: 180000
        }
      }

      const mockPaymentHistory = [
        {
          id: 1,
          date: '2024-01-15',
          term: 'Second Term',
          amount: 120000,
          method: 'Bank Transfer',
          reference: 'GHC/PAY/2024/001',
          status: 'completed',
          receipt: 'receipt_001.pdf'
        },
        {
          id: 2,
          date: '2023-10-20',
          term: 'First Term',
          amount: 200000,
          method: 'Online Payment',
          reference: 'GHC/PAY/2023/045',
          status: 'completed',
          receipt: 'receipt_045.pdf'
        },
        {
          id: 3,
          date: '2023-09-15',
          term: 'First Term',
          amount: 100000,
          method: 'Cash',
          reference: 'GHC/PAY/2023/032',
          status: 'completed',
          receipt: 'receipt_032.pdf'
        }
      ]

      setPaymentData(mockPaymentData)
      setPaymentHistory(mockPaymentHistory)
    } catch (error) {
      console.error('Error loading payment data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'partial': return 'bg-yellow-100 text-yellow-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      case 'pending': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handlePayNow = () => {
    alert('Payment gateway integration will be implemented. This will redirect to secure payment portal.')
  }

  const downloadReceipt = (receiptId) => {
    alert(`Downloading receipt: ${receiptId}`)
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Fee Payments</h1>
              <p className="text-blue-100">Manage your school fee payments</p>
            </div>
            <div className="hidden md:block">
              <CreditCardIcon className="w-12 h-12 text-white opacity-80" />
            </div>
          </div>
        </div>

        {/* Student Info */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-gray-600">Student Name</p>
              <p className="font-semibold text-gray-900">{paymentData?.student.name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Student ID</p>
              <p className="font-semibold text-gray-900">{paymentData?.student.studentId}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Class</p>
              <p className="font-semibold text-gray-900">{paymentData?.student.class}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Session</p>
              <p className="font-semibold text-gray-900">{paymentData?.student.session}</p>
            </div>
          </div>
        </div>

        {/* Current Term Payment */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900">{paymentData?.currentTerm.term} Fee Payment</h3>
            <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full capitalize ${getStatusColor(paymentData?.currentTerm.status)}`}>
              {paymentData?.currentTerm.status}
            </span>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Fee Breakdown */}
            <div>
              <h4 className="font-semibold text-gray-900 mb-4">Fee Breakdown</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-600">Tuition Fee</span>
                  <span className="font-semibold">₦{paymentData?.currentTerm.tuitionFee.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-600">Development Fee</span>
                  <span className="font-semibold">₦{paymentData?.currentTerm.developmentFee.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-600">Exam Fee</span>
                  <span className="font-semibold">₦{paymentData?.currentTerm.examFee.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-600">Sports Fee</span>
                  <span className="font-semibold">₦{paymentData?.currentTerm.sportsFee.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-t-2 border-gray-200">
                  <span className="font-semibold text-gray-900">Total Fee</span>
                  <span className="font-bold text-lg">₦{paymentData?.currentTerm.totalFee.toLocaleString()}</span>
                </div>
              </div>
            </div>

            {/* Payment Status */}
            <div>
              <h4 className="font-semibold text-gray-900 mb-4">Payment Status</h4>
              <div className="space-y-4">
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-green-800 font-medium">Amount Paid</span>
                    <span className="text-green-900 font-bold text-lg">₦{paymentData?.currentTerm.amountPaid.toLocaleString()}</span>
                  </div>
                  <div className="w-full bg-green-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: `${(paymentData?.currentTerm.amountPaid / paymentData?.currentTerm.totalFee) * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div className="p-4 bg-red-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-red-800 font-medium">Outstanding Balance</span>
                    <span className="text-red-900 font-bold text-lg">₦{paymentData?.currentTerm.balance.toLocaleString()}</span>
                  </div>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-blue-800 font-medium">Due Date</span>
                    <span className="text-blue-900 font-semibold">{new Date(paymentData?.currentTerm.dueDate).toLocaleDateString()}</span>
                  </div>
                </div>

                {paymentData?.currentTerm.balance > 0 && (
                  <button
                    onClick={handlePayNow}
                    className="w-full flex items-center justify-center space-x-2 bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors font-semibold"
                  >
                    <CreditCardIcon className="w-5 h-5" />
                    <span>Pay Now - ₦{paymentData?.currentTerm.balance.toLocaleString()}</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Session Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Fees (Session)</p>
                <p className="text-2xl font-bold text-gray-900">₦{paymentData?.summary.totalFeesThisSession.toLocaleString()}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <CreditCardIcon className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Paid</p>
                <p className="text-2xl font-bold text-green-600">₦{paymentData?.summary.totalPaidThisSession.toLocaleString()}</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <CheckCircleIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Outstanding</p>
                <p className="text-2xl font-bold text-red-600">₦{paymentData?.summary.outstandingBalance.toLocaleString()}</p>
              </div>
              <div className="p-3 bg-red-50 rounded-lg">
                <ClockIcon className="w-8 h-8 text-red-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Payment History */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="px-8 py-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900">Payment History</h3>
            <p className="text-sm text-gray-600 mt-1">Your previous fee payments</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Term</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receipt</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paymentHistory.map((payment) => (
                  <tr key={payment.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {new Date(payment.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.term}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                      ₦{payment.amount.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.method}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{payment.reference}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getStatusColor(payment.status)}`}>
                        {payment.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => downloadReceipt(payment.receipt)}
                        className="text-primary hover:text-primary/80 flex items-center space-x-1"
                      >
                        <DownloadIcon className="w-4 h-4" />
                        <span>Download</span>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Available Payment Methods</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="border border-gray-200 rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <CreditCardIcon className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Online Payment</h4>
              <p className="text-sm text-gray-600">Pay securely with debit/credit cards or bank transfer</p>
            </div>
            <div className="border border-gray-200 rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 font-bold">₦</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Bank Deposit</h4>
              <p className="text-sm text-gray-600">Direct bank deposit to school account</p>
            </div>
            <div className="border border-gray-200 rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-purple-600 font-bold">💳</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">School Office</h4>
              <p className="text-sm text-gray-600">Pay directly at the school bursar's office</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

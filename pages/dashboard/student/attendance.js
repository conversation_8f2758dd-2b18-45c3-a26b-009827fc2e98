import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES, getCurrentUser } from '../../../lib/auth'

// Professional SVG Icons
const CalendarIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
)

const CheckCircleIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

const XCircleIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

const ClockIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

export default function StudentAttendance() {
  const [attendanceData, setAttendanceData] = useState(null)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth())
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const user = getCurrentUser()

  useEffect(() => {
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.STUDENT)) {
      router.push('/login')
      return
    }
    loadAttendanceData()
  }, [router, selectedMonth, selectedYear])

  const loadAttendanceData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockData = {
        student: {
          name: user?.name || 'Michael Johnson',
          studentId: 'GHC/2024/001',
          class: 'SS2A'
        },
        summary: {
          totalDays: 70,
          presentDays: 65,
          absentDays: 3,
          lateDays: 2,
          attendanceRate: 92.9
        },
        monthlyAttendance: [
          { date: '2024-12-01', status: 'present', subjects: ['Math', 'English', 'Physics', 'Chemistry'] },
          { date: '2024-12-02', status: 'present', subjects: ['Math', 'English', 'Physics', 'Chemistry'] },
          { date: '2024-12-03', status: 'absent', subjects: [], reason: 'Sick' },
          { date: '2024-12-04', status: 'present', subjects: ['Math', 'English', 'Physics', 'Chemistry'] },
          { date: '2024-12-05', status: 'late', subjects: ['English', 'Physics', 'Chemistry'], arrivalTime: '8:30 AM' },
          { date: '2024-12-06', status: 'present', subjects: ['Math', 'English', 'Physics', 'Chemistry'] },
          { date: '2024-12-09', status: 'present', subjects: ['Math', 'English', 'Physics', 'Chemistry'] },
          { date: '2024-12-10', status: 'present', subjects: ['Math', 'English', 'Physics', 'Chemistry'] },
          { date: '2024-12-11', status: 'present', subjects: ['Math', 'English', 'Physics', 'Chemistry'] },
          { date: '2024-12-12', status: 'late', subjects: ['Physics', 'Chemistry'], arrivalTime: '9:15 AM' },
          { date: '2024-12-13', status: 'present', subjects: ['Math', 'English', 'Physics', 'Chemistry'] },
          { date: '2024-12-16', status: 'present', subjects: ['Math', 'English', 'Physics', 'Chemistry'] }
        ],
        subjectAttendance: [
          { subject: 'Mathematics', present: 18, total: 20, percentage: 90.0 },
          { subject: 'English Language', present: 19, total: 20, percentage: 95.0 },
          { subject: 'Physics', present: 17, total: 20, percentage: 85.0 },
          { subject: 'Chemistry', present: 18, total: 20, percentage: 90.0 },
          { subject: 'Biology', present: 19, total: 20, percentage: 95.0 },
          { subject: 'Geography', present: 16, total: 18, percentage: 88.9 }
        ]
      }
      setAttendanceData(mockData)
    } catch (error) {
      console.error('Error loading attendance data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'present':
        return <CheckCircleIcon className="w-5 h-5 text-green-600" />
      case 'absent':
        return <XCircleIcon className="w-5 h-5 text-red-600" />
      case 'late':
        return <ClockIcon className="w-5 h-5 text-yellow-600" />
      default:
        return <CalendarIcon className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'absent':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'late':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  const years = [2023, 2024, 2025]

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">My Attendance</h1>
              <p className="text-blue-100">Track your school attendance record</p>
            </div>
            <div className="hidden md:block">
              <CalendarIcon className="w-12 h-12 text-white opacity-80" />
            </div>
          </div>
        </div>

        {/* Student Info */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-600">Student Name</p>
              <p className="font-semibold text-gray-900">{attendanceData?.student.name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Student ID</p>
              <p className="font-semibold text-gray-900">{attendanceData?.student.studentId}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Class</p>
              <p className="font-semibold text-gray-900">{attendanceData?.student.class}</p>
            </div>
          </div>
        </div>

        {/* Attendance Summary */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Days</p>
                <p className="text-3xl font-bold text-gray-900">{attendanceData?.summary.totalDays}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <CalendarIcon className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Present</p>
                <p className="text-3xl font-bold text-green-600">{attendanceData?.summary.presentDays}</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <CheckCircleIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Absent</p>
                <p className="text-3xl font-bold text-red-600">{attendanceData?.summary.absentDays}</p>
              </div>
              <div className="p-3 bg-red-50 rounded-lg">
                <XCircleIcon className="w-8 h-8 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Late</p>
                <p className="text-3xl font-bold text-yellow-600">{attendanceData?.summary.lateDays}</p>
              </div>
              <div className="p-3 bg-yellow-50 rounded-lg">
                <ClockIcon className="w-8 h-8 text-yellow-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Attendance Rate</p>
                <p className="text-3xl font-bold text-primary">{attendanceData?.summary.attendanceRate}%</p>
              </div>
              <div className="p-3 bg-primary/10 rounded-lg">
                <CheckCircleIcon className="w-8 h-8 text-primary" />
              </div>
            </div>
          </div>
        </div>

        {/* Month/Year Selection */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h3 className="text-xl font-semibold text-gray-900">Attendance Details</h3>
            <div className="flex gap-4">
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
              >
                {months.map((month, index) => (
                  <option key={index} value={index}>{month}</option>
                ))}
              </select>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
              >
                {years.map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Daily Attendance */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Daily Attendance - {months[selectedMonth]} {selectedYear}</h3>
          <div className="space-y-4">
            {attendanceData?.monthlyAttendance.map((day, index) => (
              <div key={index} className={`border rounded-lg p-4 ${getStatusColor(day.status)}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(day.status)}
                    <div>
                      <p className="font-semibold">{new Date(day.date).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}</p>
                      <p className="text-sm capitalize">{day.status}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    {day.status === 'present' && (
                      <p className="text-sm text-gray-600">{day.subjects.length} subjects attended</p>
                    )}
                    {day.status === 'late' && (
                      <p className="text-sm text-gray-600">Arrived at {day.arrivalTime}</p>
                    )}
                    {day.status === 'absent' && day.reason && (
                      <p className="text-sm text-gray-600">Reason: {day.reason}</p>
                    )}
                  </div>
                </div>
                {day.subjects.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-current border-opacity-20">
                    <p className="text-sm font-medium mb-2">Subjects:</p>
                    <div className="flex flex-wrap gap-2">
                      {day.subjects.map((subject, idx) => (
                        <span key={idx} className="px-2 py-1 bg-white bg-opacity-50 rounded text-xs">
                          {subject}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Subject-wise Attendance */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="px-8 py-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900">Subject-wise Attendance</h3>
            <p className="text-sm text-gray-600 mt-1">Your attendance record by subject</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Present</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Classes</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {attendanceData?.subjectAttendance.map((subject, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{subject.subject}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subject.present}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subject.total}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-200 rounded-full h-2 mr-3">
                          <div 
                            className={`h-2 rounded-full ${subject.percentage >= 85 ? 'bg-green-600' : subject.percentage >= 75 ? 'bg-yellow-600' : 'bg-red-600'}`}
                            style={{ width: `${subject.percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-semibold">{subject.percentage.toFixed(1)}%</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${
                        subject.percentage >= 85 ? 'bg-green-100 text-green-800' : 
                        subject.percentage >= 75 ? 'bg-yellow-100 text-yellow-800' : 
                        'bg-red-100 text-red-800'
                      }`}>
                        {subject.percentage >= 85 ? 'Excellent' : subject.percentage >= 75 ? 'Good' : 'Poor'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES, getCurrentUser } from '../../../lib/auth'

// Professional SVG Icons
const ChartBarIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
)

const DownloadIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
)

const TrendingUpIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
  </svg>
)

export default function StudentGrades() {
  const [grades, setGrades] = useState([])
  const [reportCard, setReportCard] = useState(null)
  const [selectedTerm, setSelectedTerm] = useState('current')
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const user = getCurrentUser()

  useEffect(() => {
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.STUDENT)) {
      router.push('/login')
      return
    }
    loadGradesData()
  }, [router, selectedTerm])

  const loadGradesData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockGrades = [
        { id: 1, subject: 'Mathematics', ca1: 18, ca2: 16, exam: 65, total: 99, grade: 'A', position: 2, teacher: 'Mr. Johnson' },
        { id: 2, subject: 'English Language', ca1: 15, ca2: 17, exam: 58, total: 90, grade: 'A', position: 1, teacher: 'Mrs. Smith' },
        { id: 3, subject: 'Physics', ca1: 16, ca2: 14, exam: 52, total: 82, grade: 'B+', position: 5, teacher: 'Dr. Brown' },
        { id: 4, subject: 'Chemistry', ca1: 17, ca2: 15, exam: 48, total: 80, grade: 'B+', position: 3, teacher: 'Mrs. Davis' },
        { id: 5, subject: 'Biology', ca1: 19, ca2: 18, exam: 55, total: 92, grade: 'A', position: 1, teacher: 'Mr. Wilson' },
        { id: 6, subject: 'Geography', ca1: 16, ca2: 16, exam: 50, total: 82, grade: 'B+', position: 4, teacher: 'Mrs. Taylor' },
        { id: 7, subject: 'Economics', ca1: 14, ca2: 15, exam: 45, total: 74, grade: 'B', position: 8, teacher: 'Mr. Anderson' },
        { id: 8, subject: 'Literature', ca1: 17, ca2: 16, exam: 53, total: 86, grade: 'A-', position: 2, teacher: 'Mrs. Clark' }
      ]

      const mockReportCard = {
        student: user?.name || 'Michael Johnson',
        class: user?.class || 'SS2A',
        term: 'Second Term',
        session: '2023/2024',
        totalSubjects: mockGrades.length,
        totalScore: mockGrades.reduce((sum, grade) => sum + grade.total, 0),
        average: (mockGrades.reduce((sum, grade) => sum + grade.total, 0) / mockGrades.length).toFixed(1),
        position: '3rd',
        outOf: 45,
        attendance: { present: 68, absent: 2, total: 70 },
        nextTermBegins: '2024-01-15',
        principalComment: 'Excellent performance. Keep up the good work!',
        teacherComment: 'Michael shows great potential in all subjects. Consistent effort needed in Economics.'
      }

      setGrades(mockGrades)
      setReportCard(mockReportCard)
    } catch (error) {
      console.error('Error loading grades:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getGradeColor = (grade) => {
    if (grade === 'A') return 'text-green-600 bg-green-50'
    if (grade === 'A-') return 'text-green-600 bg-green-50'
    if (grade === 'B+') return 'text-blue-600 bg-blue-50'
    if (grade === 'B') return 'text-yellow-600 bg-yellow-50'
    if (grade === 'C') return 'text-orange-600 bg-orange-50'
    return 'text-red-600 bg-red-50'
  }

  const downloadReportCard = () => {
    // In a real implementation, this would generate and download a PDF
    alert('Report card download functionality will be implemented with PDF generation')
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">My Grades & Report Card</h1>
              <p className="text-blue-100">Track your academic performance</p>
            </div>
            <div className="hidden md:block">
              <ChartBarIcon className="w-12 h-12 text-white opacity-80" />
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Overall Average</p>
                <p className="text-3xl font-bold text-gray-900">{reportCard?.average}%</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <TrendingUpIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Class Position</p>
                <p className="text-3xl font-bold text-gray-900">{reportCard?.position}</p>
                <p className="text-xs text-gray-500">out of {reportCard?.outOf}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <ChartBarIcon className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Subjects</p>
                <p className="text-3xl font-bold text-gray-900">{reportCard?.totalSubjects}</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <ChartBarIcon className="w-8 h-8 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Attendance</p>
                <p className="text-3xl font-bold text-gray-900">{Math.round((reportCard?.attendance.present / reportCard?.attendance.total) * 100)}%</p>
                <p className="text-xs text-gray-500">{reportCard?.attendance.present}/{reportCard?.attendance.total} days</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <ChartBarIcon className="w-8 h-8 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Term Selection & Download */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Select Term</label>
            <select
              value={selectedTerm}
              onChange={(e) => setSelectedTerm(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
            >
              <option value="current">Current Term (Second Term)</option>
              <option value="first">First Term</option>
              <option value="third">Third Term</option>
            </select>
          </div>
          <button
            onClick={downloadReportCard}
            className="flex items-center space-x-2 bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
          >
            <DownloadIcon className="w-5 h-5" />
            <span>Download Report Card</span>
          </button>
        </div>

        {/* Grades Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="px-8 py-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900">Subject Grades</h3>
            <p className="text-sm text-gray-600 mt-1">{reportCard?.term} - {reportCard?.session}</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CA1 (20)</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CA2 (20)</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exam (60)</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total (100)</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {grades.map((grade) => (
                  <tr key={grade.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{grade.subject}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.ca1}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.ca2}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.exam}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-lg font-semibold text-gray-900">{grade.total}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getGradeColor(grade.grade)}`}>
                        {grade.grade}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.position}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{grade.teacher}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Report Card Summary */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Report Card Summary</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h4 className="font-semibold text-gray-900 mb-4">Academic Performance</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Score:</span>
                  <span className="font-semibold">{reportCard?.totalScore}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Average:</span>
                  <span className="font-semibold">{reportCard?.average}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Class Position:</span>
                  <span className="font-semibold">{reportCard?.position} out of {reportCard?.outOf}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Attendance:</span>
                  <span className="font-semibold">{reportCard?.attendance.present}/{reportCard?.attendance.total} days</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-4">Comments</h4>
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Class Teacher's Comment:</p>
                  <p className="text-sm text-gray-800 bg-gray-50 p-3 rounded-lg">{reportCard?.teacherComment}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Principal's Comment:</p>
                  <p className="text-sm text-gray-800 bg-gray-50 p-3 rounded-lg">{reportCard?.principalComment}</p>
                </div>
                <div className="pt-2">
                  <p className="text-sm text-gray-600">Next Term Begins: <span className="font-semibold">{reportCard?.nextTermBegins}</span></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES } from '../../../lib/auth'

// Professional SVG Icons
const AcademicCapIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
  </svg>
)

const PlusIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
  </svg>
)

const EditIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
  </svg>
)

const TrashIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
  </svg>
)

const SaveIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3-3-3m3-3v12" />
  </svg>
)

export default function AdminAcademic() {
  const [activeTab, setActiveTab] = useState('classes')
  const [classes, setClasses] = useState([])
  const [subjects, setSubjects] = useState([])
  const [sessions, setSessions] = useState([])
  const [gradingSystem, setGradingSystem] = useState({})
  const [showAddModal, setShowAddModal] = useState(false)
  const [modalType, setModalType] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.ADMIN)) {
      router.push('/login')
      return
    }
    loadAcademicData()
  }, [router])

  const loadAcademicData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockClasses = [
        { id: 1, name: 'SS1A', level: 'SS1', capacity: 40, currentStudents: 35, classTeacher: 'Mrs. Smith' },
        { id: 2, name: 'SS1B', level: 'SS1', capacity: 40, currentStudents: 38, classTeacher: 'Mr. Johnson' },
        { id: 3, name: 'SS2A', level: 'SS2', capacity: 40, currentStudents: 32, classTeacher: 'Dr. Brown' },
        { id: 4, name: 'SS2B', level: 'SS2', capacity: 40, currentStudents: 36, classTeacher: 'Mrs. Davis' },
        { id: 5, name: 'SS3A', level: 'SS3', capacity: 40, currentStudents: 28, classTeacher: 'Mr. Wilson' },
        { id: 6, name: 'SS3B', level: 'SS3', capacity: 40, currentStudents: 30, classTeacher: 'Mrs. Taylor' }
      ]

      const mockSubjects = [
        { id: 1, name: 'Mathematics', code: 'MTH', category: 'Core', creditUnits: 4, teacher: 'Mr. Johnson' },
        { id: 2, name: 'English Language', code: 'ENG', category: 'Core', creditUnits: 4, teacher: 'Mrs. Smith' },
        { id: 3, name: 'Physics', code: 'PHY', category: 'Science', creditUnits: 3, teacher: 'Dr. Brown' },
        { id: 4, name: 'Chemistry', code: 'CHE', category: 'Science', creditUnits: 3, teacher: 'Mrs. Davis' },
        { id: 5, name: 'Biology', code: 'BIO', category: 'Science', creditUnits: 3, teacher: 'Mr. Wilson' },
        { id: 6, name: 'Geography', code: 'GEO', category: 'Social Science', creditUnits: 2, teacher: 'Mrs. Taylor' },
        { id: 7, name: 'Economics', code: 'ECO', category: 'Social Science', creditUnits: 2, teacher: 'Mr. Anderson' },
        { id: 8, name: 'Literature', code: 'LIT', category: 'Arts', creditUnits: 2, teacher: 'Mrs. Clark' }
      ]

      const mockSessions = [
        { id: 1, name: '2023/2024', startDate: '2023-09-15', endDate: '2024-07-20', status: 'current' },
        { id: 2, name: '2022/2023', startDate: '2022-09-15', endDate: '2023-07-20', status: 'completed' },
        { id: 3, name: '2024/2025', startDate: '2024-09-15', endDate: '2025-07-20', status: 'upcoming' }
      ]

      const mockGradingSystem = {
        type: 'percentage',
        passMarkPercentage: 40,
        grades: [
          { grade: 'A', minScore: 80, maxScore: 100, points: 5, remark: 'Excellent' },
          { grade: 'B', minScore: 70, maxScore: 79, points: 4, remark: 'Very Good' },
          { grade: 'C', minScore: 60, maxScore: 69, points: 3, remark: 'Good' },
          { grade: 'D', minScore: 50, maxScore: 59, points: 2, remark: 'Pass' },
          { grade: 'E', minScore: 40, maxScore: 49, points: 1, remark: 'Poor' },
          { grade: 'F', minScore: 0, maxScore: 39, points: 0, remark: 'Fail' }
        ]
      }

      setClasses(mockClasses)
      setSubjects(mockSubjects)
      setSessions(mockSessions)
      setGradingSystem(mockGradingSystem)
    } catch (error) {
      console.error('Error loading academic data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // Mock API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 1500))
      alert('Academic setup saved successfully!')
    } catch (error) {
      console.error('Error saving academic setup:', error)
      alert('Error saving academic setup. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleDelete = (type, id) => {
    if (confirm(`Are you sure you want to delete this ${type}?`)) {
      switch (type) {
        case 'class':
          setClasses(classes.filter(c => c.id !== id))
          break
        case 'subject':
          setSubjects(subjects.filter(s => s.id !== id))
          break
        case 'session':
          setSessions(sessions.filter(s => s.id !== id))
          break
      }
    }
  }

  const openAddModal = (type) => {
    setModalType(type)
    setShowAddModal(true)
  }

  const tabs = [
    { id: 'classes', name: 'Classes', icon: '🏫' },
    { id: 'subjects', name: 'Subjects', icon: '📚' },
    { id: 'sessions', name: 'Sessions', icon: '📅' },
    { id: 'grading', name: 'Grading System', icon: '📊' }
  ]

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Academic Setup</h1>
              <p className="text-blue-100">Configure classes, subjects, sessions, and grading system</p>
            </div>
            <div className="hidden md:block">
              <AcademicCapIcon className="w-12 h-12 text-white opacity-80" />
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Classes</p>
                <p className="text-3xl font-bold text-gray-900">{classes.length}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <AcademicCapIcon className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Subjects</p>
                <p className="text-3xl font-bold text-gray-900">{subjects.length}</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <AcademicCapIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Students</p>
                <p className="text-3xl font-bold text-gray-900">{classes.reduce((sum, c) => sum + c.currentStudents, 0)}</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <AcademicCapIcon className="w-8 h-8 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Current Session</p>
                <p className="text-lg font-bold text-gray-900">{sessions.find(s => s.status === 'current')?.name}</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <AcademicCapIcon className="w-8 h-8 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Tabs */}
          <div className="lg:w-64">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <span className="text-lg">{tab.icon}</span>
                    <span className="font-medium">{tab.name}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
              {/* Classes Tab */}
              {activeTab === 'classes' && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-semibold text-gray-900">Class Management</h3>
                    <button
                      onClick={() => openAddModal('class')}
                      className="flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      <PlusIcon className="w-5 h-5" />
                      <span>Add Class</span>
                    </button>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class Name</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Capacity</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class Teacher</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {classes.map((classItem) => (
                          <tr key={classItem.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">{classItem.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{classItem.level}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{classItem.currentStudents}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{classItem.capacity}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{classItem.classTeacher}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                <button className="text-blue-600 hover:text-blue-900 p-1 rounded">
                                  <EditIcon className="w-4 h-4" />
                                </button>
                                <button 
                                  onClick={() => handleDelete('class', classItem.id)}
                                  className="text-red-600 hover:text-red-900 p-1 rounded"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Subjects Tab */}
              {activeTab === 'subjects' && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-semibold text-gray-900">Subject Management</h3>
                    <button
                      onClick={() => openAddModal('subject')}
                      className="flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      <PlusIcon className="w-5 h-5" />
                      <span>Add Subject</span>
                    </button>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject Name</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credit Units</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {subjects.map((subject) => (
                          <tr key={subject.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">{subject.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subject.code}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subject.category}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subject.creditUnits}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{subject.teacher}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                <button className="text-blue-600 hover:text-blue-900 p-1 rounded">
                                  <EditIcon className="w-4 h-4" />
                                </button>
                                <button 
                                  onClick={() => handleDelete('subject', subject.id)}
                                  className="text-red-600 hover:text-red-900 p-1 rounded"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Sessions Tab */}
              {activeTab === 'sessions' && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-semibold text-gray-900">Academic Sessions</h3>
                    <button
                      onClick={() => openAddModal('session')}
                      className="flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      <PlusIcon className="w-5 h-5" />
                      <span>Add Session</span>
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {sessions.map((session) => (
                      <div key={session.id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex justify-between items-start mb-4">
                          <h4 className="text-lg font-semibold text-gray-900">{session.name}</h4>
                          <span className={`px-3 py-1 text-xs font-semibold rounded-full ${
                            session.status === 'current' ? 'bg-green-100 text-green-800' :
                            session.status === 'upcoming' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {session.status}
                          </span>
                        </div>
                        <div className="space-y-2 text-sm text-gray-600">
                          <p><span className="font-medium">Start:</span> {new Date(session.startDate).toLocaleDateString()}</p>
                          <p><span className="font-medium">End:</span> {new Date(session.endDate).toLocaleDateString()}</p>
                        </div>
                        <div className="flex items-center space-x-2 mt-4">
                          <button className="text-blue-600 hover:text-blue-900 p-1 rounded">
                            <EditIcon className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => handleDelete('session', session.id)}
                            className="text-red-600 hover:text-red-900 p-1 rounded"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Grading System Tab */}
              {activeTab === 'grading' && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-gray-900">Grading System Configuration</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Grading Type</label>
                      <select
                        value={gradingSystem.type}
                        onChange={(e) => setGradingSystem({...gradingSystem, type: e.target.value})}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                      >
                        <option value="percentage">Percentage</option>
                        <option value="points">Points</option>
                        <option value="letter">Letter Grade</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Pass Mark (%)</label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={gradingSystem.passMarkPercentage}
                        onChange={(e) => setGradingSystem({...gradingSystem, passMarkPercentage: parseInt(e.target.value)})}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
                      />
                    </div>
                  </div>

                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Min Score</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max Score</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remark</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {gradingSystem.grades?.map((grade, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap font-medium text-gray-900">{grade.grade}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.minScore}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.maxScore}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.points}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{grade.remark}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Save Button */}
              <div className="flex justify-end pt-6 border-t border-gray-200 mt-8">
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors ${
                    isSaving 
                      ? 'bg-gray-400 cursor-not-allowed' 
                      : 'bg-primary hover:bg-primary/90'
                  } text-white`}
                >
                  <SaveIcon className="w-5 h-5" />
                  <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

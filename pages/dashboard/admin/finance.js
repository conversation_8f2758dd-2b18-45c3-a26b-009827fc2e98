import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import DashboardLayout from '../../../components/DashboardLayout'
import { isAuthenticated, hasRole, AUTH_ROLES } from '../../../lib/auth'

// Professional SVG Icons
const CurrencyIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
)

const TrendingUpIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
  </svg>
)

const ChartBarIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
)

export default function AdminFinance() {
  const [financeData, setFinanceData] = useState(null)
  const [selectedPeriod, setSelectedPeriod] = useState('current-term')
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated() || !hasRole(AUTH_ROLES.ADMIN)) {
      router.push('/login')
      return
    }
    loadFinanceData()
  }, [router, selectedPeriod])

  const loadFinanceData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockData = {
        overview: {
          totalRevenue: 45000000,
          totalExpenses: 28000000,
          netIncome: 17000000,
          outstandingFees: 8500000,
          collectionRate: 84.2
        },
        revenueBreakdown: [
          { category: 'Tuition Fees', amount: 35000000, percentage: 77.8 },
          { category: 'Development Fees', amount: 6000000, percentage: 13.3 },
          { category: 'Exam Fees', amount: 2500000, percentage: 5.6 },
          { category: 'Sports Fees', amount: 1500000, percentage: 3.3 }
        ],
        expenseBreakdown: [
          { category: 'Staff Salaries', amount: 18000000, percentage: 64.3 },
          { category: 'Utilities', amount: 3500000, percentage: 12.5 },
          { category: 'Maintenance', amount: 2800000, percentage: 10.0 },
          { category: 'Supplies', amount: 2200000, percentage: 7.9 },
          { category: 'Others', amount: 1500000, percentage: 5.4 }
        ],
        recentTransactions: [
          { id: 1, date: '2024-12-15', description: 'Student Fee Payment - Michael Johnson', type: 'income', amount: 200000, reference: 'GHC/PAY/2024/001' },
          { id: 2, date: '2024-12-14', description: 'Staff Salary - December 2024', type: 'expense', amount: 1500000, reference: 'GHC/SAL/2024/12' },
          { id: 3, date: '2024-12-13', description: 'Electricity Bill Payment', type: 'expense', amount: 450000, reference: 'GHC/UTL/2024/045' },
          { id: 4, date: '2024-12-12', description: 'Student Fee Payment - Sarah Williams', type: 'income', amount: 180000, reference: 'GHC/PAY/2024/002' },
          { id: 5, date: '2024-12-11', description: 'Laboratory Equipment Purchase', type: 'expense', amount: 850000, reference: 'GHC/EQP/2024/023' }
        ],
        monthlyTrends: [
          { month: 'Aug', income: 4200000, expenses: 2800000 },
          { month: 'Sep', income: 4500000, expenses: 2900000 },
          { month: 'Oct', income: 4100000, expenses: 2750000 },
          { month: 'Nov', income: 4300000, expenses: 2850000 },
          { month: 'Dec', income: 4600000, expenses: 3100000 }
        ]
      }
      setFinanceData(mockData)
    } catch (error) {
      console.error('Error loading finance data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary to-blue-700 rounded-xl shadow-lg p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Finance Reports</h1>
              <p className="text-blue-100">Monitor school financial performance</p>
            </div>
            <div className="hidden md:block">
              <CurrencyIcon className="w-12 h-12 text-white opacity-80" />
            </div>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Revenue</p>
                <p className="text-2xl font-bold text-green-600">₦{(financeData?.overview.totalRevenue / 1000000).toFixed(1)}M</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <TrendingUpIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Expenses</p>
                <p className="text-2xl font-bold text-red-600">₦{(financeData?.overview.totalExpenses / 1000000).toFixed(1)}M</p>
              </div>
              <div className="p-3 bg-red-50 rounded-lg">
                <ChartBarIcon className="w-8 h-8 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Net Income</p>
                <p className="text-2xl font-bold text-blue-600">₦{(financeData?.overview.netIncome / 1000000).toFixed(1)}M</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <CurrencyIcon className="w-8 h-8 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Outstanding</p>
                <p className="text-2xl font-bold text-orange-600">₦{(financeData?.overview.outstandingFees / 1000000).toFixed(1)}M</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <ChartBarIcon className="w-8 h-8 text-orange-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Collection Rate</p>
                <p className="text-2xl font-bold text-purple-600">{financeData?.overview.collectionRate}%</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <TrendingUpIcon className="w-8 h-8 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Period Selection */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-gray-900">Financial Analysis</h3>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary"
            >
              <option value="current-term">Current Term</option>
              <option value="current-session">Current Session</option>
              <option value="last-session">Last Session</option>
            </select>
          </div>
        </div>

        {/* Revenue & Expense Breakdown */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Revenue Breakdown */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Revenue Breakdown</h3>
            <div className="space-y-4">
              {financeData?.revenueBreakdown.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">{item.category}</span>
                      <span className="text-sm text-gray-600">{item.percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${item.percentage}%` }}
                      ></div>
                    </div>
                    <div className="text-right mt-1">
                      <span className="text-sm font-semibold text-gray-900">₦{(item.amount / 1000000).toFixed(1)}M</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Expense Breakdown */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Expense Breakdown</h3>
            <div className="space-y-4">
              {financeData?.expenseBreakdown.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">{item.category}</span>
                      <span className="text-sm text-gray-600">{item.percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-red-600 h-2 rounded-full" 
                        style={{ width: `${item.percentage}%` }}
                      ></div>
                    </div>
                    <div className="text-right mt-1">
                      <span className="text-sm font-semibold text-gray-900">₦{(item.amount / 1000000).toFixed(1)}M</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Transactions */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="px-8 py-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900">Recent Transactions</h3>
            <p className="text-sm text-gray-600 mt-1">Latest financial transactions</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {financeData?.recentTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {new Date(transaction.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">{transaction.description}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${
                        transaction.type === 'income' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {transaction.type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold">
                      <span className={transaction.type === 'income' ? 'text-green-600' : 'text-red-600'}>
                        {transaction.type === 'income' ? '+' : '-'}₦{transaction.amount.toLocaleString()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{transaction.reference}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
